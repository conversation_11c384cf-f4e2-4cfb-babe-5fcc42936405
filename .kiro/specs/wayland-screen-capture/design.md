# 设计文档

## 概述

本项目将开发一个名为`screen-capture`的Rust库，用于在Linux环境下同时支持X11和Wayland桌面环境的屏幕截图和录制功能。该库将自动检测当前的显示服务器类型，在Wayland环境下通过xdg-desktop-portal协议与桌面环境通信处理权限申请，在X11环境下直接使用X11 API进行屏幕捕获，并提供统一简洁的API接口供开发者使用。

核心设计原则：
- 安全性：遵循Wayland的安全模型，通过标准协议申请权限
- 易用性：提供简单直观的API接口
- 高性能：高效的资源管理和异步处理
- 可扩展性：支持多种输出格式和配置选项

## 架构

### 整体架构

```mermaid
graph TB
    A[用户应用] --> B[Public API Layer]
    B --> C[Configuration Manager]
    B --> D[Operation Controller]
    D --> E[Display Server Detector]
    D --> F[Permission Manager]
    D --> G[Capture Engine]
    D --> H[File Manager]
    
    E --> I{显示服务器类型}
    I -->|Wayland| J[Wayland路径]
    I -->|X11| K[X11路径]
    
    J --> F
    F --> L[xdg-desktop-portal]
    J --> M[PipeWire Backend]
    
    K --> N[X11 Backend]
    N --> O[X11 API]
    
    G --> M
    G --> N
    G --> P[Video Encoder]
    H --> Q[File System]
    
    L --> R[GNOME Shell / KDE / 其他桌面环境]
    M --> S[Wayland Compositor]
```

### 模块分层

1. **API层** - 对外提供的公共接口
2. **控制层** - 操作控制和协调
3. **服务层** - 核心功能实现
4. **系统层** - 与系统和桌面环境的交互

## 组件和接口

### 1. Public API Layer

```rust
pub struct ScreenCapture {
    config: CaptureConfig,
    controller: OperationController,
}

pub struct CaptureConfig {
    pub operation_type: OperationType,
    pub output_directory: PathBuf,
    pub output_format: OutputFormat,
    pub duration: Option<Duration>,
    pub interval: Option<Duration>,
    pub quality: Option<u8>,
}

pub enum OperationType {
    Screenshot,
    Recording,
    IntervalScreenshot,
}

pub enum OutputFormat {
    // 图片格式
    PNG,
    JPEG,
    BMP,
    // 视频格式
    MP4,
    WebM,
    AVI,
}

impl ScreenCapture {
    pub fn new(config: CaptureConfig) -> Result<Self, CaptureError>;
    pub async fn execute(&mut self) -> Result<CaptureResult, CaptureError>;
    pub async fn stop(&mut self) -> Result<(), CaptureError>;
}
```

### 2. Configuration Manager

```rust
pub struct ConfigurationManager {
    default_config: CaptureConfig,
}

impl ConfigurationManager {
    pub fn validate_config(config: &CaptureConfig) -> Result<(), ConfigError>;
    pub fn apply_defaults(config: &mut CaptureConfig);
    pub fn check_permissions(output_dir: &Path) -> Result<(), PermissionError>;
}
```

### 3. Operation Controller

```rust
pub struct OperationController {
    permission_manager: PermissionManager,
    capture_engine: CaptureEngine,
    file_manager: FileManager,
    state: OperationState,
}

pub enum OperationState {
    Idle,
    RequestingPermission,
    Capturing,
    Recording,
    IntervalCapturing,
    Stopping,
}

impl OperationController {
    pub async fn execute_screenshot(&mut self, config: &CaptureConfig) -> Result<PathBuf, CaptureError>;
    pub async fn execute_recording(&mut self, config: &CaptureConfig) -> Result<PathBuf, CaptureError>;
    pub async fn execute_interval_screenshot(&mut self, config: &CaptureConfig) -> Result<Vec<PathBuf>, CaptureError>;
    pub async fn stop_operation(&mut self) -> Result<(), CaptureError>;
}
```

### 4. Permission Manager

```rust
pub struct PermissionManager {
    portal_connection: Option<PortalConnection>,
    session_handle: Option<String>,
}

impl PermissionManager {
    pub async fn request_screen_capture_permission(&mut self) -> Result<PermissionToken, PermissionError>;
    pub async fn check_permission_status(&self) -> Result<PermissionStatus, PermissionError>;
    pub fn cleanup_session(&mut self) -> Result<(), PermissionError>;
}

pub struct PermissionToken {
    pub session_handle: String,
    pub node_id: u32,
    pub expires_at: SystemTime,
}
```

### 5. Display Server Detector

```rust
pub struct DisplayServerDetector;

pub enum DisplayServerType {
    X11,
    Wayland,
}

impl DisplayServerDetector {
    pub fn detect() -> Result<DisplayServerType, DetectionError>;
    pub fn is_wayland() -> bool;
    pub fn is_x11() -> bool;
}
```

### 6. Capture Engine

```rust
pub struct CaptureEngine {
    backend: Box<dyn CaptureBackend>,
    encoder: Option<VideoEncoder>,
}

pub trait CaptureBackend {
    async fn capture_frame(&mut self, token: Option<&PermissionToken>) -> Result<RawFrame, CaptureError>;
    async fn start_recording(&mut self, token: Option<&PermissionToken>) -> Result<RecordingSession, CaptureError>;
    async fn stop_recording(&mut self, session: RecordingSession) -> Result<Vec<u8>, CaptureError>;
    fn requires_permission(&self) -> bool;
}

pub struct PipeWireCaptureBackend {
    stream: Option<PipeWireStream>,
    node_id: Option<u32>,
}

impl CaptureBackend for PipeWireCaptureBackend {
    fn requires_permission(&self) -> bool { true }
    // ... 其他实现
}

pub struct X11CaptureBackend {
    display: Option<X11Display>,
    screen_info: Option<ScreenInfo>,
}

impl CaptureBackend for X11CaptureBackend {
    fn requires_permission(&self) -> bool { false }
    // ... 其他实现
}

pub struct VideoEncoder {
    codec: VideoCodec,
    quality: u8,
    frame_rate: u32,
}
```

### 6. File Manager

```rust
pub struct FileManager {
    output_directory: PathBuf,
}

impl FileManager {
    pub fn save_image(&self, data: &[u8], format: ImageFormat, filename: &str) -> Result<PathBuf, FileError>;
    pub fn save_video(&self, data: &[u8], format: VideoFormat, filename: &str) -> Result<PathBuf, FileError>;
    pub fn generate_filename(&self, operation_type: OperationType, format: OutputFormat) -> String;
    pub fn check_disk_space(&self, estimated_size: u64) -> Result<(), FileError>;
}
```

## 数据模型

### 核心数据结构

```rust
pub struct RawFrame {
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
    pub stride: u32,
    pub format: PixelFormat,
    pub timestamp: SystemTime,
}

pub struct RecordingSession {
    pub session_id: String,
    pub start_time: SystemTime,
    pub frame_rate: u32,
    pub resolution: (u32, u32),
}

pub struct CaptureResult {
    pub operation_type: OperationType,
    pub output_files: Vec<PathBuf>,
    pub duration: Option<Duration>,
    pub total_frames: Option<u32>,
}
```

### 错误类型

```rust
#[derive(Debug, thiserror::Error)]
pub enum CaptureError {
    #[error("权限错误: {0}")]
    Permission(#[from] PermissionError),
    
    #[error("配置错误: {0}")]
    Configuration(#[from] ConfigError),
    
    #[error("文件操作错误: {0}")]
    File(#[from] FileError),
    
    #[error("捕获错误: {0}")]
    Capture(String),
    
    #[error("编码错误: {0}")]
    Encoding(String),
    
    #[error("系统错误: {0}")]
    System(String),
}
```

## 错误处理

### 错误处理策略

1. **权限错误处理**
   - 自动重试权限申请（最多3次）
   - 提供详细的权限拒绝原因
   - 支持权限状态查询

2. **文件系统错误处理**
   - 磁盘空间检查
   - 目录权限验证
   - 文件名冲突处理

3. **捕获错误处理**
   - 连接断开自动重连
   - 帧丢失检测和恢复
   - 编码失败回退机制

4. **资源清理**
   - RAII模式确保资源释放
   - 异常情况下的安全清理
   - 超时处理机制

### 日志记录

```rust
pub struct Logger {
    level: LogLevel,
    output: LogOutput,
}

pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

// 使用tracing crate进行结构化日志记录
```

## 测试策略

### 单元测试

1. **配置验证测试**
   - 参数有效性验证
   - 默认值应用测试
   - 边界条件测试

2. **权限管理测试**
   - 权限申请流程测试
   - 权限状态查询测试
   - 会话管理测试

3. **文件操作测试**
   - 文件保存测试
   - 文件名生成测试
   - 磁盘空间检查测试

### 集成测试

1. **端到端测试**
   - 完整截图流程测试
   - 完整录制流程测试
   - 间隔截图流程测试

2. **桌面环境兼容性测试**
   - GNOME环境测试
   - KDE环境测试
   - 其他Wayland合成器测试

3. **性能测试**
   - 内存使用测试
   - CPU使用测试
   - 长时间运行稳定性测试

### 模拟测试

```rust
pub struct MockCaptureBackend {
    frames: Vec<RawFrame>,
    current_frame: usize,
}

pub struct MockPermissionManager {
    should_grant: bool,
    delay: Duration,
}
```

## 依赖项

### 核心依赖

```toml
[dependencies]
tokio = { version = "1.0", features = ["full"] }
zbus = "3.0"  # D-Bus通信
pipewire = "0.7"  # PipeWire集成
gstreamer = "0.20"  # 视频编码
image = "0.24"  # 图像处理
thiserror = "1.0"  # 错误处理
tracing = "0.1"  # 日志记录
serde = { version = "1.0", features = ["derive"] }  # 序列化
uuid = "1.0"  # UUID生成
x11 = "2.21"  # X11 API绑定
xcb = "1.2"  # X11 C绑定
```

### 开发依赖

```toml
[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
tempfile = "3.0"
criterion = "0.5"  # 性能测试
```

## 部署考虑

### 系统要求

- Linux内核 5.10+
- Wayland合成器支持
- xdg-desktop-portal安装
- PipeWire 0.3+

### 运行时依赖

- libpipewire-0.3
- libgstreamer-1.0
- xdg-desktop-portal-gtk 或 xdg-desktop-portal-kde

### 权限要求

- 无需特殊系统权限
- 通过xdg-desktop-portal申请屏幕访问权限
- 需要目标目录的写入权限
//! 错误类型定义

use std::path::PathBuf;
use std::time::Duration;
use thiserror::Error;

/// 主要的错误类型
#[derive(Debug, Error, Clone)]
pub enum CaptureError {
    #[error("权限错误: {0}")]
    Permission(#[from] PermissionError),
    
    #[error("配置错误: {0}")]
    Configuration(#[from] ConfigError),
    
    #[error("文件操作错误: {0}")]
    File(#[from] FileError),
    
    #[error("捕获错误: {0}")]
    Capture(String),
    
    #[error("编码错误: {0}")]
    Encoding(String),
    
    #[error("系统错误: {0}")]
    System(String),
    
    #[error("后端错误: {0}")]
    Backend(String),
    
    #[error("操作被取消")]
    Cancelled,
    
    #[error("操作超时")]
    Timeout,
    
    #[error("资源不足: {0}")]
    InsufficientResources(String),
    
    #[error("不支持的操作: {0}")]
    UnsupportedOperation(String),
    
    #[error("显示服务器错误: {0}")]
    DisplayServer(#[from] DisplayServerError),
    
    #[error("无效的状态转换: 从 {from:?} 到 {to:?}")]
    InvalidStateTransition {
        from: crate::controller::OperationState,
        to: crate::controller::OperationState,
    },
}

/// 显示服务器相关错误
#[derive(Debug, Error, Clone)]
pub enum DisplayServerError {
    #[error("不支持的显示服务器类型")]
    UnsupportedType,
    
    #[error("无法检测显示服务器类型")]
    DetectionFailed,
    
    #[error("X11环境不可用")]
    X11Unavailable,
    
    #[error("Wayland环境不可用")]
    WaylandUnavailable,
    
    #[error("GNOME Shell扩展不可用")]
    GnomeExtensionUnavailable,
    
    #[error("显示服务器连接错误: {0}")]
    ConnectionError(String),
    
    #[error("多显示器配置错误: {0}")]
    MultiMonitorError(String),
}

/// 权限相关错误
#[derive(Debug, Error, Clone)]
pub enum PermissionError {
    #[error("权限被拒绝")]
    Denied,
    
    #[error("权限申请超时")]
    Timeout,
    
    #[error("xdg-desktop-portal不可用")]
    PortalUnavailable,
    
    #[error("D-Bus连接错误: {0}")]
    DBusError(String),
    
    #[error("会话无效或已过期")]
    InvalidSession,
    
    #[error("桌面环境不支持屏幕捕获")]
    UnsupportedEnvironment,
}

/// 配置相关错误
#[derive(Debug, Error, Clone)]
pub enum ConfigError {
    #[error("无效的输出目录: {path}")]
    InvalidOutputDirectory { path: PathBuf },
    
    #[error("不支持的输出格式: {format}")]
    UnsupportedFormat { format: String },
    
    #[error("无效的时长设置: {duration:?}")]
    InvalidDuration { duration: Duration },
    
    #[error("无效的质量设置: {quality} (应该在1-100之间)")]
    InvalidQuality { quality: u8 },
    
    #[error("无效的间隔设置: {interval:?}")]
    InvalidInterval { interval: Duration },
    
    #[error("参数冲突: {message}")]
    ConflictingParameters { message: String },
}

/// 文件操作相关错误
#[derive(Debug, Error, Clone)]
pub enum FileError {
    #[error("IO错误: {0}")]
    Io(String),
    
    #[error("权限不足: {path}")]
    PermissionDenied { path: PathBuf },
    
    #[error("磁盘空间不足")]
    InsufficientSpace,
    
    #[error("文件已存在: {path}")]
    FileExists { path: PathBuf },
    
    #[error("无效的文件名: {name}")]
    InvalidFileName { name: String },
    
    #[error("无效的输出目录: {path}")]
    InvalidOutputDirectory { path: PathBuf },
    
    #[error("图像编码错误: {0}")]
    ImageEncoding(String),
    
    #[error("视频编码错误: {0}")]
    VideoEncoding(String),
}

/// 操作结果
#[derive(Debug, Clone)]
pub struct CaptureResult {
    /// 操作类型
    pub operation_type: crate::config::OperationType,
    
    /// 输出文件列表
    pub output_files: Vec<PathBuf>,
    
    /// 操作持续时间
    pub duration: Option<Duration>,
    
    /// 总帧数（仅录制时有效）
    pub total_frames: Option<u32>,
    
    /// 文件总大小（字节）
    pub total_size: u64,
}

impl CaptureResult {
    /// 创建新的结果实例
    pub fn new(operation_type: crate::config::OperationType) -> Self {
        Self {
            operation_type,
            output_files: Vec::new(),
            duration: None,
            total_frames: None,
            total_size: 0,
        }
    }
    
    /// 添加输出文件
    pub fn add_file(&mut self, path: PathBuf, size: u64) {
        self.output_files.push(path);
        self.total_size += size;
    }
    
    /// 设置持续时间
    pub fn set_duration(&mut self, duration: Duration) {
        self.duration = Some(duration);
    }
    
    /// 设置总帧数
    pub fn set_total_frames(&mut self, frames: u32) {
        self.total_frames = Some(frames);
    }
}

/// 结果类型别名
pub type Result<T> = std::result::Result<T, CaptureError>;

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_capture_error_display() {
        let permission_error = PermissionError::Denied;
        let capture_error = CaptureError::Permission(permission_error);
        assert!(capture_error.to_string().contains("权限错误"));
        assert!(capture_error.to_string().contains("权限被拒绝"));
        
        let display_error = DisplayServerError::UnsupportedType;
        let capture_error = CaptureError::DisplayServer(display_error);
        assert!(capture_error.to_string().contains("显示服务器错误"));
        assert!(capture_error.to_string().contains("不支持的显示服务器类型"));
    }

    #[test]
    fn test_display_server_error_variants() {
        let errors = vec![
            DisplayServerError::UnsupportedType,
            DisplayServerError::DetectionFailed,
            DisplayServerError::X11Unavailable,
            DisplayServerError::WaylandUnavailable,
            DisplayServerError::GnomeExtensionUnavailable,
            DisplayServerError::ConnectionError("连接失败".to_string()),
            DisplayServerError::MultiMonitorError("多显示器配置错误".to_string()),
        ];

        for error in errors {
            // 确保所有错误变体都能正确显示
            assert!(!error.to_string().is_empty());
        }
    }

    #[test]
    fn test_permission_error_variants() {
        let errors = vec![
            PermissionError::Denied,
            PermissionError::Timeout,
            PermissionError::PortalUnavailable,
            PermissionError::DBusError("test error".to_string()),
            PermissionError::InvalidSession,
            PermissionError::UnsupportedEnvironment,
        ];

        for error in errors {
            // 确保所有错误变体都能正确显示
            assert!(!error.to_string().is_empty());
        }
    }

    #[test]
    fn test_config_error_variants() {
        let temp_path = std::env::temp_dir();
        let errors = vec![
            ConfigError::InvalidOutputDirectory { path: temp_path },
            ConfigError::UnsupportedFormat { format: "test".to_string() },
            ConfigError::InvalidDuration { duration: Duration::from_secs(0) },
            ConfigError::InvalidQuality { quality: 150 },
            ConfigError::InvalidInterval { interval: Duration::from_millis(50) },
            ConfigError::ConflictingParameters { message: "test conflict".to_string() },
        ];

        for error in errors {
            assert!(!error.to_string().is_empty());
        }
    }

    #[test]
    fn test_file_error_variants() {
        let temp_path = std::env::temp_dir();
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "test");
        
        let errors = vec![
            FileError::Io(io_error),
            FileError::PermissionDenied { path: temp_path.clone() },
            FileError::InsufficientSpace,
            FileError::FileExists { path: temp_path },
            FileError::InvalidFileName { name: "test".to_string() },
            FileError::ImageEncoding("test error".to_string()),
            FileError::VideoEncoding("test error".to_string()),
        ];

        for error in errors {
            assert!(!error.to_string().is_empty());
        }
    }

    #[test]
    fn test_capture_result_creation() {
        let mut result = CaptureResult::new(crate::config::OperationType::Screenshot);
        assert_eq!(result.operation_type, crate::config::OperationType::Screenshot);
        assert!(result.output_files.is_empty());
        assert_eq!(result.total_size, 0);
        assert!(result.duration.is_none());
        assert!(result.total_frames.is_none());
    }

    #[test]
    fn test_capture_result_add_file() {
        let mut result = CaptureResult::new(crate::config::OperationType::Screenshot);
        let file_path = std::env::temp_dir().join("test.png");
        let file_size = 1024;

        result.add_file(file_path.clone(), file_size);
        
        assert_eq!(result.output_files.len(), 1);
        assert_eq!(result.output_files[0], file_path);
        assert_eq!(result.total_size, file_size);
    }

    #[test]
    fn test_capture_result_set_duration() {
        let mut result = CaptureResult::new(crate::config::OperationType::Recording);
        let duration = Duration::from_secs(60);
        
        result.set_duration(duration);
        assert_eq!(result.duration, Some(duration));
    }

    #[test]
    fn test_capture_result_set_total_frames() {
        let mut result = CaptureResult::new(crate::config::OperationType::Recording);
        let frames = 1800; // 60秒 * 30fps
        
        result.set_total_frames(frames);
        assert_eq!(result.total_frames, Some(frames));
    }

    #[test]
    fn test_error_conversion() {
        // 测试错误类型转换
        let permission_error = PermissionError::Denied;
        let capture_error: CaptureError = permission_error.into();
        
        match capture_error {
            CaptureError::Permission(PermissionError::Denied) => {},
            _ => panic!("错误转换失败"),
        }
    }

    #[test]
    fn test_result_type_alias() {
        // 测试Result类型别名
        fn test_function() -> Result<String> {
            Ok("success".to_string())
        }
        
        let result = test_function();
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
    }
}
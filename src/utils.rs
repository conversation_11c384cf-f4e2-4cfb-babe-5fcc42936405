//! 工具函数模块

use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// 时间工具
pub mod time {
    use super::*;
    
    /// 获取当前时间戳（毫秒）
    pub fn current_timestamp_ms() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }
    
    /// 获取当前时间戳（秒）
    pub fn current_timestamp_secs() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
    
    /// 格式化持续时间为人类可读格式
    pub fn format_duration(duration: Duration) -> String {
        let total_secs = duration.as_secs();
        let hours = total_secs / 3600;
        let minutes = (total_secs % 3600) / 60;
        let seconds = total_secs % 60;
        
        if hours > 0 {
            format!("{:02}:{:02}:{:02}", hours, minutes, seconds)
        } else {
            format!("{:02}:{:02}", minutes, seconds)
        }
    }
}

/// 文件系统工具
pub mod fs {
    use std::path::Path;
    use std::fs;
    
    /// 检查路径是否可写
    pub fn is_writable<P: AsRef<Path>>(path: P) -> bool {
        let path = path.as_ref();
        
        if !path.exists() {
            return false;
        }
        
        // 尝试创建临时文件来测试写入权限
        let temp_file = path.join(".temp_write_test");
        match fs::write(&temp_file, b"test") {
            Ok(_) => {
                let _ = fs::remove_file(&temp_file);
                true
            }
            Err(_) => false,
        }
    }
    
    /// 获取可用磁盘空间（字节）
    pub fn available_space<P: AsRef<Path>>(path: P) -> std::io::Result<u64> {
        // TODO: 实现跨平台的磁盘空间检查
        // 这将在后续任务中实现更详细的磁盘空间检查
        Ok(u64::MAX) // 临时返回最大值
    }
    
    /// 安全地创建目录
    pub fn ensure_dir<P: AsRef<Path>>(path: P) -> std::io::Result<()> {
        let path = path.as_ref();
        if !path.exists() {
            fs::create_dir_all(path)?;
        }
        Ok(())
    }
}

/// 字符串工具
pub mod string {
    /// 清理文件名，移除非法字符
    pub fn sanitize_filename(filename: &str) -> String {
        filename
            .chars()
            .map(|c| match c {
                '<' | '>' | ':' | '"' | '|' | '?' | '*' | '/' | '\\' => '_',
                c if c.is_control() => '_',
                c => c,
            })
            .collect()
    }
    
    /// 截断字符串到指定长度
    pub fn truncate(s: &str, max_len: usize) -> String {
        if s.len() <= max_len {
            s.to_string()
        } else {
            format!("{}...", &s[..max_len.saturating_sub(3)])
        }
    }
}

/// 系统信息工具
pub mod system {
    /// 检查是否在Wayland环境中运行
    pub fn is_wayland() -> bool {
        std::env::var("WAYLAND_DISPLAY").is_ok()
    }
    
    /// 检查是否在X11环境中运行
    pub fn is_x11() -> bool {
        std::env::var("DISPLAY").is_ok()
    }
    
    /// 获取桌面环境名称
    pub fn desktop_environment() -> Option<String> {
        // 检查常见的桌面环境变量
        for var in &[
            "XDG_CURRENT_DESKTOP",
            "DESKTOP_SESSION",
            "GDMSESSION",
        ] {
            if let Ok(value) = std::env::var(var) {
                if !value.is_empty() {
                    return Some(value.to_lowercase());
                }
            }
        }
        None
    }
    
    /// 检查xdg-desktop-portal是否可用
    pub fn is_portal_available() -> bool {
        // TODO: 实现实际的portal可用性检查
        // 这将在后续任务中实现
        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_time_formatting() {
        let duration = Duration::from_secs(3661); // 1小时1分1秒
        let formatted = time::format_duration(duration);
        assert_eq!(formatted, "01:01:01");
        
        let duration = Duration::from_secs(61); // 1分1秒
        let formatted = time::format_duration(duration);
        assert_eq!(formatted, "01:01");
    }

    #[test]
    fn test_filename_sanitization() {
        let dirty = "file<name>with:bad*chars?.txt";
        let clean = string::sanitize_filename(dirty);
        assert_eq!(clean, "file_name_with_bad_chars_.txt");
    }

    #[test]
    fn test_string_truncation() {
        let long_string = "This is a very long string that should be truncated";
        let truncated = string::truncate(long_string, 20);
        assert!(truncated.len() <= 20);
        assert!(truncated.ends_with("..."));
    }

    #[test]
    fn test_system_detection() {
        // 这些测试可能会根据运行环境而有所不同
        let _is_wayland = system::is_wayland();
        let _is_x11 = system::is_x11();
        let _desktop = system::desktop_environment();
        
        // 至少应该能够调用这些函数而不出错
        assert!(true);
    }
}
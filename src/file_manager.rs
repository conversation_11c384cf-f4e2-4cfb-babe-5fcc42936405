//! 文件管理模块

use std::path::{Path, PathBuf};
use std::fs;
use std::io::{<PERSON>urs<PERSON>, Write};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use chrono::{DateTime, Local};
use image::{ImageFormat as ImgFormat, ImageEncoder};
use image::codecs::png::PngEncoder;
use image::codecs::jpeg::JpegEncoder;
use image::codecs::bmp::BmpEncoder;
use gstreamer as gst;
use gstreamer::prelude::*;
use gstreamer_app as gst_app;
use crate::config::{OperationType, OutputFormat};
use crate::error::{FileError, Result};
use crate::backend::common::{RawFrame, PixelFormat};

/// 图像格式枚举
#[derive(Debug, Clone, Copy)]
pub enum ImageFormat {
    PNG,
    JPEG,
    BMP,
    WEBP,
}

/// 视频格式枚举
#[derive(Debug, Clone, Copy)]
pub enum VideoFormat {
    MP4,
    WebM,
    AVI,
    MKV,
}

/// 文件管理器
#[derive(Debug, Clone)]
pub struct FileManager {
    /// 输出目录
    output_directory: PathBuf,
    /// 文件名前缀
    filename_prefix: Option<String>,
    /// 是否覆盖已存在的文件
    overwrite_existing: bool,
}

impl FileManager {
    /// 创建新的文件管理器
    pub fn new<P: AsRef<Path>>(output_directory: P) -> Result<Self> {
        let output_directory = output_directory.as_ref().to_path_buf();
        
        // 检查目录是否存在，如果不存在则尝试创建
        if !output_directory.exists() {
            fs::create_dir_all(&output_directory).map_err(|e| {
                FileError::PermissionDenied {
                    path: output_directory.clone(),
                }
            })?;
        }
        
        // 检查是否有写入权限
        let metadata = fs::metadata(&output_directory).map_err(|e| FileError::Io(e.to_string()))?;
        
        if !metadata.is_dir() {
            return Err(FileError::InvalidOutputDirectory {
                path: output_directory,
            }.into());
        }
        
        // 尝试创建一个临时文件来测试写入权限
        let test_file = output_directory.join(".write_test");
        match fs::write(&test_file, b"test") {
            Ok(_) => {
                // 成功写入，删除测试文件
                let _ = fs::remove_file(&test_file);
            },
            Err(_) => {
                return Err(FileError::PermissionDenied {
                    path: output_directory,
                }.into());
            }
        }
        
        Ok(Self {
            output_directory,
            filename_prefix: None,
            overwrite_existing: false,
        })
    }
    
    /// 创建带有自定义选项的文件管理器
    pub fn with_options<P: AsRef<Path>>(
        output_directory: P,
        filename_prefix: Option<String>,
        overwrite_existing: bool,
    ) -> Result<Self> {
        let mut manager = Self::new(output_directory)?;
        manager.filename_prefix = filename_prefix;
        manager.overwrite_existing = overwrite_existing;
        Ok(manager)
    }
    
    /// 获取输出目录
    pub fn get_output_directory(&self) -> &Path {
        &self.output_directory
    }
    
    /// 设置输出目录
    pub fn set_output_directory<P: AsRef<Path>>(&mut self, output_directory: P) -> Result<()> {
        let output_directory = output_directory.as_ref().to_path_buf();
        
        // 检查目录是否存在，如果不存在则尝试创建
        if !output_directory.exists() {
            fs::create_dir_all(&output_directory).map_err(|_| {
                FileError::PermissionDenied {
                    path: output_directory.clone(),
                }
            })?;
        }
        
        // 检查是否有写入权限
        let metadata = fs::metadata(&output_directory).map_err(|e| FileError::Io(e.to_string()))?;
        
        if !metadata.is_dir() {
            return Err(FileError::InvalidOutputDirectory {
                path: output_directory,
            }.into());
        }
        
        // 尝试创建一个临时文件来测试写入权限
        let test_file = output_directory.join(".write_test");
        match fs::write(&test_file, b"test") {
            Ok(_) => {
                // 成功写入，删除测试文件
                let _ = fs::remove_file(&test_file);
            },
            Err(_) => {
                return Err(FileError::PermissionDenied {
                    path: output_directory,
                }.into());
            }
        }
        
        self.output_directory = output_directory;
        Ok(())
    }
    
    /// 保存图像文件
    pub fn save_image(&self, data: &[u8], format: ImageFormat, filename: &str) -> Result<PathBuf> {
        let file_path = self.output_directory.join(filename);
        
        // 检查文件是否已存在
        if file_path.exists() && !self.overwrite_existing {
            return Err(FileError::FileExists { path: file_path }.into());
        }
        
        // 确保目录存在
        if let Some(parent) = file_path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent).map_err(|e| FileError::Io(e.to_string()))?;
            }
        }
        
        // 检查磁盘空间是否足够
        self.check_disk_space(data.len() as u64)?;
        
        // 保存文件
        fs::write(&file_path, data).map_err(|e| FileError::Io(e.to_string()))?;
        
        Ok(file_path)
    }
    
    /// 从原始帧保存图像文件
    pub fn save_frame_as_image(&self, frame: &RawFrame, format: ImageFormat, filename: &str, quality: u8) -> Result<PathBuf> {
        let file_path = self.output_directory.join(filename);
        
        // 检查文件是否已存在
        if file_path.exists() && !self.overwrite_existing {
            return Err(FileError::FileExists { path: file_path }.into());
        }
        
        // 确保目录存在
        if let Some(parent) = file_path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent).map_err(|e| FileError::Io(e.to_string()))?;
            }
        }
        
        // 检查磁盘空间是否足够（估计大小）
        let estimated_size = frame.width as u64 * frame.height as u64 * 4; // 4字节/像素
        self.check_disk_space(estimated_size)?;
        
        // 创建文件
        let mut file = fs::File::create(&file_path).map_err(|e| FileError::Io(e.to_string()))?;
        
        // 根据格式编码图像
        match format {
            ImageFormat::PNG => {
                let encoder = PngEncoder::new(file);
                encoder.write_image(
                    &frame.data,
                    frame.width,
                    frame.height,
                    match frame.format {
                        crate::backend::common::PixelFormat::RGBA8 => image::ColorType::Rgba8,
                        crate::backend::common::PixelFormat::BGRA8 => {
                            // 需要转换BGRA到RGBA
                            let mut rgba_data = Vec::with_capacity(frame.data.len());
                            for chunk in frame.data.chunks(4) {
                                if chunk.len() == 4 {
                                    rgba_data.push(chunk[2]); // R <- B
                                    rgba_data.push(chunk[1]); // G <- G
                                    rgba_data.push(chunk[0]); // B <- R
                                    rgba_data.push(chunk[3]); // A <- A
                                }
                            }
                            image::ColorType::Rgba8
                        },
                        crate::backend::common::PixelFormat::RGB8 => image::ColorType::Rgb8,
                        crate::backend::common::PixelFormat::BGR8 => {
                            // 需要转换BGR到RGB
                            let mut rgb_data = Vec::with_capacity(frame.data.len());
                            for chunk in frame.data.chunks(3) {
                                if chunk.len() == 3 {
                                    rgb_data.push(chunk[2]); // R <- B
                                    rgb_data.push(chunk[1]); // G <- G
                                    rgb_data.push(chunk[0]); // B <- R
                                }
                            }
                            image::ColorType::Rgb8
                        },
                    }
                ).map_err(|e| FileError::ImageEncoding(e.to_string()))?;
            },
            ImageFormat::JPEG => {
                let mut encoder = JpegEncoder::new_with_quality(file, quality);
                encoder.encode(
                    &frame.data,
                    frame.width,
                    frame.height,
                    match frame.format {
                        crate::backend::common::PixelFormat::RGBA8 => image::ColorType::Rgba8,
                        crate::backend::common::PixelFormat::BGRA8 => {
                            // 需要转换BGRA到RGBA
                            let mut rgba_data = Vec::with_capacity(frame.data.len());
                            for chunk in frame.data.chunks(4) {
                                if chunk.len() == 4 {
                                    rgba_data.push(chunk[2]); // R <- B
                                    rgba_data.push(chunk[1]); // G <- G
                                    rgba_data.push(chunk[0]); // B <- R
                                    rgba_data.push(chunk[3]); // A <- A
                                }
                            }
                            image::ColorType::Rgba8
                        },
                        crate::backend::common::PixelFormat::RGB8 => image::ColorType::Rgb8,
                        crate::backend::common::PixelFormat::BGR8 => {
                            // 需要转换BGR到RGB
                            let mut rgb_data = Vec::with_capacity(frame.data.len());
                            for chunk in frame.data.chunks(3) {
                                if chunk.len() == 3 {
                                    rgb_data.push(chunk[2]); // R <- B
                                    rgb_data.push(chunk[1]); // G <- G
                                    rgb_data.push(chunk[0]); // B <- R
                                }
                            }
                            image::ColorType::Rgb8
                        },
                    }
                ).map_err(|e| FileError::ImageEncoding(e.to_string()))?;
            },
            ImageFormat::BMP => {
                let mut encoder = BmpEncoder::new(&mut file);
                encoder.encode(
                    &frame.data,
                    frame.width,
                    frame.height,
                    match frame.format {
                        crate::backend::common::PixelFormat::RGBA8 => image::ColorType::Rgba8,
                        crate::backend::common::PixelFormat::BGRA8 => {
                            // 需要转换BGRA到RGBA
                            let mut rgba_data = Vec::with_capacity(frame.data.len());
                            for chunk in frame.data.chunks(4) {
                                if chunk.len() == 4 {
                                    rgba_data.push(chunk[2]); // R <- B
                                    rgba_data.push(chunk[1]); // G <- G
                                    rgba_data.push(chunk[0]); // B <- R
                                    rgba_data.push(chunk[3]); // A <- A
                                }
                            }
                            image::ColorType::Rgba8
                        },
                        crate::backend::common::PixelFormat::RGB8 => image::ColorType::Rgb8,
                        crate::backend::common::PixelFormat::BGR8 => {
                            // 需要转换BGR到RGB
                            let mut rgb_data = Vec::with_capacity(frame.data.len());
                            for chunk in frame.data.chunks(3) {
                                if chunk.len() == 3 {
                                    rgb_data.push(chunk[2]); // R <- B
                                    rgb_data.push(chunk[1]); // G <- G
                                    rgb_data.push(chunk[0]); // B <- R
                                }
                            }
                            image::ColorType::Rgb8
                        },
                    }
                ).map_err(|e| FileError::ImageEncoding(e.to_string()))?;
            },
            ImageFormat::WEBP => {
                // 使用image库的DynamicImage来处理WEBP格式
                let img = image::DynamicImage::ImageRgba8(
                    image::RgbaImage::from_raw(
                        frame.width,
                        frame.height,
                        frame.data.clone()
                    ).ok_or_else(|| FileError::ImageEncoding("Failed to create image".to_string()))?
                );
                
                img.save_with_format(&file_path, ImgFormat::WebP)
                    .map_err(|e| FileError::ImageEncoding(e.to_string()))?;
            },
        }
        
        Ok(file_path)
    }
    
    /// 从原始帧自动保存图像文件
    pub fn save_frame_as_image_auto(&self, frame: &RawFrame, format: ImageFormat, operation_type: OperationType, quality: u8) -> Result<PathBuf> {
        let filename = self.generate_unique_filename(operation_type, match format {
            ImageFormat::PNG => OutputFormat::PNG,
            ImageFormat::JPEG => OutputFormat::JPEG,
            ImageFormat::BMP => OutputFormat::BMP,
            ImageFormat::WEBP => OutputFormat::WEBP,
        });
        
        self.save_frame_as_image(frame, format, &filename, quality)
    }
    
    /// 保存图像文件，自动生成文件名
    pub fn save_image_auto(&self, data: &[u8], format: ImageFormat, operation_type: OperationType) -> Result<PathBuf> {
        let filename = self.generate_unique_filename(operation_type, match format {
            ImageFormat::PNG => OutputFormat::PNG,
            ImageFormat::JPEG => OutputFormat::JPEG,
            ImageFormat::BMP => OutputFormat::BMP,
            ImageFormat::WEBP => OutputFormat::WEBP,
        });
        
        self.save_image(data, format, &filename)
    }
    
    /// 保存视频文件
    pub fn save_video(&self, data: &[u8], format: VideoFormat, filename: &str) -> Result<PathBuf> {
        let file_path = self.output_directory.join(filename);
        
        // 检查文件是否已存在
        if file_path.exists() && !self.overwrite_existing {
            return Err(FileError::FileExists { path: file_path }.into());
        }
        
        // 确保目录存在
        if let Some(parent) = file_path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent).map_err(|e| FileError::Io(e.to_string()))?;
            }
        }
        
        // 检查磁盘空间是否足够
        self.check_disk_space(data.len() as u64)?;
        
        // 保存文件
        fs::write(&file_path, data).map_err(|e| FileError::Io(e.to_string()))?;
        
        Ok(file_path)
    }
    
    /// 保存帧序列为视频文件
    pub fn save_frames_as_video(&self, frames: &[RawFrame], format: VideoFormat, filename: &str, frame_rate: u32, quality: u8) -> Result<PathBuf> {
        use crate::video_encoder::{VideoEncoderBuilder, VideoEncoder};
        
        let file_path = self.output_directory.join(filename);
        
        // 检查文件是否已存在
        if file_path.exists() && !self.overwrite_existing {
            return Err(FileError::FileExists { path: file_path }.into());
        }
        
        // 确保目录存在
        if let Some(parent) = file_path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent).map_err(|e| FileError::Io(e.to_string()))?;
            }
        }
        
        // 检查是否有帧
        if frames.is_empty() {
            return Err(FileError::VideoEncoding("没有帧可以编码".to_string()).into());
        }
        
        // 估计视频大小（每帧大小 * 帧数）
        let estimated_size = frames[0].data.len() as u64 * frames.len() as u64;
        self.check_disk_space(estimated_size)?;
        
        // 创建视频编码器
        let self_arc = Arc::new(self.clone());
        let mut encoder = VideoEncoderBuilder::new(self_arc)
            .format(format)
            .frame_rate(frame_rate)
            .quality(quality)
            .output_path(&file_path)
            .build()?;
        
        // 初始化编码器
        encoder.initialize(frames[0].width, frames[0].height)?;
        
        // 添加所有帧
        for frame in frames {
            encoder.add_frame(frame)?;
        }
        
        // 完成编码
        encoder.finalize()?;
        
        Ok(file_path)
    }
    
    /// 保存帧序列为视频文件，自动生成文件名
    pub fn save_frames_as_video_auto(&self, frames: &[RawFrame], format: VideoFormat, operation_type: OperationType, frame_rate: u32, quality: u8) -> Result<PathBuf> {
        let filename = self.generate_unique_filename(operation_type, match format {
            VideoFormat::MP4 => OutputFormat::MP4,
            VideoFormat::WebM => OutputFormat::WebM,
            VideoFormat::AVI => OutputFormat::AVI,
            VideoFormat::MKV => OutputFormat::MKV,
        });
        
        self.save_frames_as_video(frames, format, &filename, frame_rate, quality)
    }
    
    /// 保存视频文件，自动生成文件名
    pub fn save_video_auto(&self, data: &[u8], format: VideoFormat, operation_type: OperationType) -> Result<PathBuf> {
        let filename = self.generate_unique_filename(operation_type, match format {
            VideoFormat::MP4 => OutputFormat::MP4,
            VideoFormat::WebM => OutputFormat::WebM,
            VideoFormat::AVI => OutputFormat::AVI,
            VideoFormat::MKV => OutputFormat::MKV,
        });
        
        self.save_video(data, format, &filename)
    }
    
    /// 生成文件名
    pub fn generate_filename(&self, operation_type: OperationType, format: OutputFormat) -> String {
        let now: DateTime<Local> = Local::now();
        let timestamp = now.format("%Y%m%d_%H%M%S");
        
        // 使用自定义前缀或默认前缀
        let prefix = if let Some(custom_prefix) = &self.filename_prefix {
            custom_prefix.clone()
        } else {
            match operation_type {
                OperationType::Screenshot => "screenshot".to_string(),
                OperationType::Recording => "recording".to_string(),
                OperationType::IntervalScreenshot => "interval_screenshot".to_string(),
            }
        };
        
        format!("{}_{}.{}", prefix, timestamp, format.extension())
    }
    
    /// 生成唯一文件名（处理文件名冲突）
    pub fn generate_unique_filename(&self, operation_type: OperationType, format: OutputFormat) -> String {
        let base_filename = self.generate_filename(operation_type, format);
        
        // 如果允许覆盖已存在的文件，直接返回基本文件名
        if self.overwrite_existing {
            return base_filename;
        }
        
        // 检查文件是否已存在，如果存在则添加序号
        let file_path = self.output_directory.join(&base_filename);
        if !file_path.exists() {
            return base_filename;
        }
        
        // 文件已存在，添加序号
        let file_stem = file_path.file_stem().unwrap().to_string_lossy().to_string();
        let extension = format.extension();
        
        for i in 1..1000 {
            let new_filename = format!("{}_{:03}.{}", file_stem, i, extension);
            let new_path = self.output_directory.join(&new_filename);
            if !new_path.exists() {
                return new_filename;
            }
        }
        
        // 如果尝试了1000个序号还是存在冲突，使用纳秒级时间戳确保唯一性
        let nanos = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .subsec_nanos();
        
        format!("{}_{}.{}", file_stem, nanos, extension)
    }
    
    /// 生成带序号的文件名（用于间隔截图）
    pub fn generate_numbered_filename(
        &self,
        operation_type: OperationType,
        format: OutputFormat,
        sequence: u32,
    ) -> String {
        let now: DateTime<Local> = Local::now();
        let timestamp = now.format("%Y%m%d_%H%M%S");
        
        let prefix = match operation_type {
            OperationType::Screenshot => "screenshot",
            OperationType::Recording => "recording",
            OperationType::IntervalScreenshot => "interval_screenshot",
        };
        
        format!("{}_{}_{:04}.{}", prefix, timestamp, sequence, format.extension())
    }
    
    /// 检查磁盘空间
    pub fn check_disk_space(&self, estimated_size: u64) -> Result<()> {
        // 获取目录所在的磁盘信息
        #[cfg(target_family = "unix")]
        {
            use std::os::unix::fs::MetadataExt;
            
            let metadata = fs::metadata(&self.output_directory).map_err(|e| FileError::Io(e.to_string()))?;
            let fs_stats = nix::sys::statvfs::statvfs(&self.output_directory)
                .map_err(|e| FileError::Io(format!("Failed to get disk stats: {}", e)))?;
            
            // 计算可用空间
            let block_size = fs_stats.block_size() as u64;
            let available_blocks = fs_stats.blocks_available() as u64;
            let available_space = block_size * available_blocks;
            
            // 检查是否有足够的空间
            if available_space < estimated_size {
                return Err(FileError::InsufficientSpace.into());
            }
        }
        
        #[cfg(target_family = "windows")]
        {
            use std::os::windows::fs::MetadataExt;
            
            // 获取驱动器信息
            let path_str = self.output_directory.to_string_lossy();
            let mut sectors_per_cluster: u32 = 0;
            let mut bytes_per_sector: u32 = 0;
            let mut number_of_free_clusters: u32 = 0;
            let mut total_number_of_clusters: u32 = 0;
            
            unsafe {
                let result = windows_sys::Win32::Storage::FileSystem::GetDiskFreeSpaceA(
                    path_str.as_ptr() as *const u8,
                    &mut sectors_per_cluster,
                    &mut bytes_per_sector,
                    &mut number_of_free_clusters,
                    &mut total_number_of_clusters,
                );
                
                if result == 0 {
                    return Err(FileError::Io(std::io::Error::last_os_error().to_string()).into());
                }
            }
            
            // 计算可用空间
            let available_space = (sectors_per_cluster as u64) * 
                                 (bytes_per_sector as u64) * 
                                 (number_of_free_clusters as u64);
            
            // 检查是否有足够的空间
            if available_space < estimated_size {
                return Err(FileError::InsufficientSpace.into());
            }
        }
        
        // 如果不是Unix或Windows，简单地返回成功
        #[cfg(not(any(target_family = "unix", target_family = "windows")))]
        {
            // 在其他平台上，我们可能无法检查磁盘空间
            // 这里可以添加一个警告日志
        }
        
        Ok(())
    }
    
    /// 获取文件大小
    pub fn get_file_size<P: AsRef<Path>>(&self, path: P) -> Result<u64> {
        let metadata = fs::metadata(path).map_err(|e| FileError::Io(e.to_string()))?;
        Ok(metadata.len())
    }
    
    /// 删除文件
    pub fn delete_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        fs::remove_file(path).map_err(|e| FileError::Io(e.to_string()))?;
        Ok(())
    }
    
    /// 检查文件名是否有效
    pub fn validate_filename(filename: &str) -> Result<()> {
        if filename.is_empty() {
            return Err(FileError::InvalidFileName {
                name: filename.to_string(),
            }.into());
        }
        
        // 检查是否包含非法字符
        let invalid_chars = ['<', '>', ':', '"', '|', '?', '*'];
        if filename.chars().any(|c| invalid_chars.contains(&c)) {
            return Err(FileError::InvalidFileName {
                name: filename.to_string(),
            }.into());
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_file_manager_creation() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::new(temp_dir.path());
        assert!(manager.is_ok());
    }

    #[test]
    fn test_file_manager_with_options() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::with_options(
            temp_dir.path(),
            Some("custom_prefix".to_string()),
            true,
        );
        assert!(manager.is_ok());
        
        let manager = manager.unwrap();
        assert_eq!(manager.filename_prefix, Some("custom_prefix".to_string()));
        assert!(manager.overwrite_existing);
    }

    #[test]
    fn test_filename_generation() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::new(temp_dir.path()).unwrap();
        
        let filename = manager.generate_filename(
            OperationType::Screenshot,
            OutputFormat::PNG,
        );
        
        assert!(filename.starts_with("screenshot_"));
        assert!(filename.ends_with(".png"));
        
        // 测试自定义前缀
        let manager = FileManager::with_options(
            temp_dir.path(),
            Some("custom".to_string()),
            false,
        ).unwrap();
        
        let filename = manager.generate_filename(
            OperationType::Screenshot,
            OutputFormat::PNG,
        );
        
        assert!(filename.starts_with("custom_"));
        assert!(filename.ends_with(".png"));
    }

    #[test]
    fn test_unique_filename_generation() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::new(temp_dir.path()).unwrap();
        
        let filename1 = manager.generate_unique_filename(
            OperationType::Screenshot,
            OutputFormat::PNG,
        );
        
        // 创建一个同名文件
        let file_path = temp_dir.path().join(&filename1);
        fs::write(&file_path, b"test").unwrap();
        
        // 生成另一个文件名，应该不同
        let filename2 = manager.generate_unique_filename(
            OperationType::Screenshot,
            OutputFormat::PNG,
        );
        
        assert_ne!(filename1, filename2);
    }

    #[test]
    fn test_save_image() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::new(temp_dir.path()).unwrap();
        
        // 创建测试数据
        let data = vec![0u8; 100];
        
        // 保存图像
        let result = manager.save_image(&data, ImageFormat::PNG, "test.png");
        assert!(result.is_ok());
        
        // 验证文件已创建
        let file_path = temp_dir.path().join("test.png");
        assert!(file_path.exists());
        
        // 尝试再次保存同名文件，应该失败
        let result = manager.save_image(&data, ImageFormat::PNG, "test.png");
        assert!(result.is_err());
        
        // 使用覆盖选项
        let manager = FileManager::with_options(
            temp_dir.path(),
            None,
            true,
        ).unwrap();
        
        // 现在应该可以覆盖
        let result = manager.save_image(&data, ImageFormat::PNG, "test.png");
        assert!(result.is_ok());
    }

    #[test]
    fn test_save_image_auto() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::new(temp_dir.path()).unwrap();
        
        // 创建测试数据
        let data = vec![0u8; 100];
        
        // 自动保存图像
        let result = manager.save_image_auto(&data, ImageFormat::PNG, OperationType::Screenshot);
        assert!(result.is_ok());
        
        // 验证文件已创建
        let file_path = result.unwrap();
        assert!(file_path.exists());
        
        // 文件名应该包含"screenshot"
        let filename = file_path.file_name().unwrap().to_string_lossy();
        assert!(filename.to_string().contains("screenshot"));
    }

    #[test]
    fn test_save_video() {
        let temp_dir = TempDir::new().unwrap();
        let manager = FileManager::new(temp_dir.path()).unwrap();
        
        // 创建测试数据
        let data = vec![0u8; 100];
        
        // 保存视频
        let result = manager.save_video(&data, VideoFormat::MP4, "test.mp4");
        assert!(result.is_ok());
        
        // 验证文件已创建
        let file_path = temp_dir.path().join("test.mp4");
        assert!(file_path.exists());
    }

    #[test]
    fn test_filename_validation() {
        assert!(FileManager::validate_filename("valid_filename.png").is_ok());
        assert!(FileManager::validate_filename("invalid<filename.png").is_err());
        assert!(FileManager::validate_filename("").is_err());
    }
}
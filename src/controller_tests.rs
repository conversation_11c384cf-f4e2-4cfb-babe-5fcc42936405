//! 控制器集成测试

#[cfg(test)]
mod tests {
    use crate::controller::{CaptureController, OperationState};
    use crate::config::{CaptureConfig, OperationType, OutputFormat};
    use crate::backend::common::{MockCaptureBackend, RawFrame, PixelFormat};
    use crate::error::CaptureError;
    use std::path::PathBuf;
    use std::time::Duration;
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use tempfile::TempDir;

    // 创建测试配置
    fn create_test_config() -> (TempDir, CaptureConfig) {
        let temp_dir = TempDir::new().unwrap();
        let config = CaptureConfig {
            operation_type: OperationType::Screenshot,
            output_directory: temp_dir.path().to_path_buf(),
            output_format: OutputFormat::PNG,
            quality: Some(90),
            ..Default::default()
        };
        
        (temp_dir, config)
    }
    
    #[tokio::test]
    async fn test_screenshot_execution() {
        // 创建测试配置和临时目录
        let (_temp_dir, config) = create_test_config();
        
        // 创建控制器
        let mut controller = CaptureController::new().await.unwrap();
        
        // 执行截图操作
        let result = controller.execute(&config).await;
        
        // 验证结果
        assert!(result.is_ok(), "截图操作应该成功");
        
        let capture_result = result.unwrap();
        assert_eq!(capture_result.operation_type, OperationType::Screenshot);
        assert_eq!(capture_result.output_files.len(), 1);
        
        // 验证文件是否存在
        let output_file = &capture_result.output_files[0];
        assert!(output_file.exists(), "输出文件应该存在");
        
        // 验证文件扩展名
        assert_eq!(output_file.extension().unwrap(), "png");
        
        // 验证文件大小
        assert!(capture_result.total_size > 0);
        
        // 验证持续时间
        assert!(capture_result.duration.is_some());
    }
    
    #[tokio::test]
    async fn test_screenshot_with_different_formats() {
        // 测试不同的输出格式
        let formats = vec![
            OutputFormat::PNG,
            OutputFormat::JPEG,
            OutputFormat::BMP,
            OutputFormat::WEBP,
        ];
        
        for format in formats {
            // 创建测试配置和临时目录
            let (_temp_dir, mut config) = create_test_config();
            config.output_format = format;
            
            // 创建控制器
            let mut controller = OperationController::new(&config).unwrap();
            
            // 执行截图操作
            let result = controller.execute(&config).await;
            
            // 验证结果
            assert!(result.is_ok(), "使用格式 {:?} 的截图操作应该成功", format);
            
            let capture_result = result.unwrap();
            assert_eq!(capture_result.output_files.len(), 1);
            
            // 验证文件扩展名
            let output_file = &capture_result.output_files[0];
            assert_eq!(output_file.extension().unwrap(), format.extension());
        }
    }
    
    #[tokio::test]
    async fn test_screenshot_error_handling() {
        // 创建测试配置和临时目录
        let (_temp_dir, mut config) = create_test_config();
        
        // 设置不支持的格式
        config.output_format = OutputFormat::MP4;
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 执行截图操作
        let result = controller.execute(&config).await;
        
        // 验证结果
        assert!(result.is_err(), "使用不支持的格式应该失败");
        
        match result.unwrap_err() {
            CaptureError::Configuration(_) => {
                // 预期的错误类型
            },
            err => {
                panic!("预期配置错误，但得到: {:?}", err);
            }
        }
    }
    
    #[tokio::test]
    async fn test_screenshot_timeout() {
        // 创建测试配置和临时目录
        let (_temp_dir, config) = create_test_config();
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 设置非常短的超时时间
        controller.set_timeout(Duration::from_nanos(1));
        
        // 执行截图操作
        let result = controller.execute(&config).await;
        
        // 由于我们使用的是模拟后端，这个测试可能不会真正超时
        // 但在实际环境中，这将测试超时处理
    }
    
    #[tokio::test]
    async fn test_screenshot_state_transitions() {
        // 创建测试配置和临时目录
        let (_temp_dir, config) = create_test_config();
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 订阅状态变更事件
        let mut receiver = controller.subscribe_to_state_changes();
        
        // 执行截图操作
        let handle = tokio::spawn({
            let config = config.clone();
            let controller = Arc::new(Mutex::new(controller));
            let controller_clone = controller.clone();
            
            async move {
                let mut controller = controller_clone.lock().await;
                controller.execute(&config).await
            }
        });
        
        // 收集状态变更
        let mut states = Vec::new();
        
        // 设置超时，防止测试无限等待
        let timeout = tokio::time::sleep(Duration::from_secs(5));
        
        tokio::select! {
            _ = timeout => {
                panic!("测试超时");
            }
            _ = async {
                while let Ok(event) = receiver.recv().await {
                    states.push(event.current_state);
                    if event.current_state == OperationState::Idle {
                        break;
                    }
                }
            } => {}
        }
        
        // 等待截图任务完成
        let _ = handle.await.unwrap();
        
        // 验证状态转换序列
        assert!(states.contains(&OperationState::Initializing), "应该包含初始化状态");
        assert!(states.contains(&OperationState::Capturing), "应该包含捕获状态");
        assert!(states.contains(&OperationState::Processing), "应该包含处理状态");
        assert!(states.contains(&OperationState::Idle), "应该包含空闲状态");
    }
}
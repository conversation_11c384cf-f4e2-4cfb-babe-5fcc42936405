//! 日志记录模块
//! 
//! 提供结构化日志记录功能，支持不同级别的日志输出和调试模式。

use tracing::Level;
#[cfg(feature = "tracing-subscriber")]
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};
use std::io;

/// 日志配置
#[derive(Debug, Clone)]
pub struct LogConfig {
    /// 日志级别
    pub level: Level,
    /// 是否启用详细日志
    pub verbose: bool,
    /// 是否启用调试模式
    pub debug: bool,
    /// 是否输出到文件
    pub log_to_file: bool,
    /// 日志文件路径
    pub log_file_path: Option<std::path::PathBuf>,
    /// 是否显示时间戳
    pub show_timestamp: bool,
    /// 是否显示目标模块
    pub show_target: bool,
    /// 是否显示线程ID
    pub show_thread_id: bool,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: Level::INFO,
            verbose: false,
            debug: false,
            log_to_file: false,
            log_file_path: None,
            show_timestamp: true,
            show_target: false,
            show_thread_id: false,
        }
    }
}

impl LogConfig {
    /// 创建新的日志配置
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 设置日志级别
    pub fn with_level(mut self, level: Level) -> Self {
        self.level = level;
        self
    }
    
    /// 启用详细日志
    pub fn with_verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        if verbose {
            self.level = Level::DEBUG;
        }
        self
    }
    
    /// 启用调试模式
    pub fn with_debug(mut self, debug: bool) -> Self {
        self.debug = debug;
        if debug {
            self.level = Level::TRACE;
            self.show_target = true;
            self.show_thread_id = true;
        }
        self
    }
    
    /// 设置日志文件
    pub fn with_log_file<P: Into<std::path::PathBuf>>(mut self, path: P) -> Self {
        self.log_file_path = Some(path.into());
        self.log_to_file = true;
        self
    }
    
    /// 显示时间戳
    pub fn with_timestamp(mut self, show: bool) -> Self {
        self.show_timestamp = show;
        self
    }
    
    /// 显示目标模块
    pub fn with_target(mut self, show: bool) -> Self {
        self.show_target = show;
        self
    }
    
    /// 显示线程ID
    pub fn with_thread_id(mut self, show: bool) -> Self {
        self.show_thread_id = show;
        self
    }
}

/// 初始化日志系统
#[cfg(feature = "tracing-subscriber")]
pub fn init_logging(config: LogConfig) -> Result<(), Box<dyn std::error::Error>> {
    // 创建环境过滤器
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| {
            let level_str = match config.level {
                Level::TRACE => "trace",
                Level::DEBUG => "debug", 
                Level::INFO => "info",
                Level::WARN => "warn",
                Level::ERROR => "error",
            };
            
            if config.debug {
                format!("wayland_screen_capture={},debug", level_str)
            } else if config.verbose {
                format!("wayland_screen_capture={}", level_str)
            } else {
                format!("wayland_screen_capture={}", level_str)
            }.into()
        });
    
    // 创建格式化层
    let fmt_layer = fmt::layer()
        .with_writer(io::stdout)
        .with_ansi(true)
        .with_level(true)
        .with_target(config.show_target)
        .with_thread_ids(config.show_thread_id)
        .with_span_events(if config.debug { 
            FmtSpan::ENTER | FmtSpan::EXIT 
        } else { 
            FmtSpan::NONE 
        });
    
    // 根据配置设置时间戳
    let fmt_layer = if config.show_timestamp {
        fmt_layer.boxed()
    } else {
        fmt_layer.without_time().boxed()
    };
    
    // 创建订阅器
    let subscriber = tracing_subscriber::registry()
        .with(env_filter)
        .with(fmt_layer);
    
    // 如果需要输出到文件，添加文件层
    if config.log_to_file {
        if let Some(ref log_path) = config.log_file_path {
            // 确保日志目录存在
            if let Some(parent) = log_path.parent() {
                std::fs::create_dir_all(parent)?;
            }
            
            let file = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(log_path)?;
            
            let file_layer = fmt::layer()
                .with_writer(file)
                .with_ansi(false)
                .with_level(true)
                .with_target(config.show_target)
                .with_thread_ids(config.show_thread_id);
            
            subscriber.with(file_layer).init();
        } else {
            subscriber.init();
        }
    } else {
        subscriber.init();
    }
    
    // 记录初始化信息
    tracing::info!("日志系统初始化完成");
    tracing::debug!("日志配置: {:?}", config);
    
    Ok(())
}

/// 初始化日志系统（无 tracing-subscriber 特性时的简化版本）
#[cfg(not(feature = "tracing-subscriber"))]
pub fn init_logging(_config: LogConfig) -> Result<(), Box<dyn std::error::Error>> {
    // 简化版本，仅输出基本信息
    println!("日志系统初始化完成（简化模式）");
    Ok(())
}

/// 便利函数：初始化默认日志
pub fn init_default_logging() -> Result<(), Box<dyn std::error::Error>> {
    init_logging(LogConfig::default())
}

/// 便利函数：初始化详细日志
pub fn init_verbose_logging() -> Result<(), Box<dyn std::error::Error>> {
    init_logging(LogConfig::new().with_verbose(true))
}

/// 便利函数：初始化调试日志
pub fn init_debug_logging() -> Result<(), Box<dyn std::error::Error>> {
    init_logging(LogConfig::new().with_debug(true))
}

/// 便利函数：初始化文件日志
pub fn init_file_logging<P: Into<std::path::PathBuf>>(
    path: P
) -> Result<(), Box<dyn std::error::Error>> {
    init_logging(LogConfig::new().with_log_file(path))
}

/// 记录操作开始
#[macro_export]
macro_rules! log_operation_start {
    ($operation:expr) => {
        tracing::info!("开始执行操作: {}", $operation);
    };
    ($operation:expr, $($field:tt)*) => {
        tracing::info!("开始执行操作: {}", $operation, $($field)*);
    };
}

/// 记录操作完成
#[macro_export]
macro_rules! log_operation_complete {
    ($operation:expr, $duration:expr) => {
        tracing::info!("操作完成: {}, 耗时: {:?}", $operation, $duration);
    };
    ($operation:expr, $duration:expr, $($field:tt)*) => {
        tracing::info!("操作完成: {}, 耗时: {:?}", $operation, $duration, $($field)*);
    };
}

/// 记录操作失败
#[macro_export]
macro_rules! log_operation_error {
    ($operation:expr, $error:expr) => {
        tracing::error!("操作失败: {}, 错误: {}", $operation, $error);
    };
    ($operation:expr, $error:expr, $($field:tt)*) => {
        tracing::error!("操作失败: {}, 错误: {}", $operation, $error, $($field)*);
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    
    #[test]
    fn test_log_config_creation() {
        let config = LogConfig::new()
            .with_level(Level::DEBUG)
            .with_verbose(true)
            .with_debug(false)
            .with_timestamp(true)
            .with_target(false);
        
        assert_eq!(config.level, Level::DEBUG);
        assert!(config.verbose);
        assert!(!config.debug);
        assert!(config.show_timestamp);
        assert!(!config.show_target);
    }
    
    #[test]
    fn test_log_config_debug_mode() {
        let config = LogConfig::new().with_debug(true);
        
        assert_eq!(config.level, Level::TRACE);
        assert!(config.debug);
        assert!(config.show_target);
        assert!(config.show_thread_id);
    }
    
    #[test]
    fn test_log_config_file_logging() {
        let temp_dir = tempdir().unwrap();
        let log_path = temp_dir.path().join("test.log");
        
        let config = LogConfig::new().with_log_file(&log_path);
        
        assert!(config.log_to_file);
        assert_eq!(config.log_file_path, Some(log_path));
    }
}

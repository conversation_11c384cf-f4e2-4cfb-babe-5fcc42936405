//! 控制器模块，负责协调捕获操作

use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, oneshot};
use tokio::time::{interval, sleep, timeout};
use crate::backend::{CaptureBackend, detect_display_server, DisplayServerType};
use crate::backend::common::{RawFrame, RecordingSession};
use crate::config::{CaptureConfig, OperationType, OutputFormat};
use crate::error::{CaptureError, CaptureResult, Result};
use crate::file_manager::{FileManager, ImageFormat, VideoFormat};
use crate::permission::{PermissionManager, create_permission_manager, PermissionToken};

/// 操作状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OperationState {
    /// 空闲状态
    Idle,
    /// 初始化中
    Initializing,
    /// 申请权限中
    RequestingPermission,
    /// 准备中
    Preparing,
    /// 捕获中
    Capturing,
    /// 录制中
    Recording,
    /// 间隔截图中
    IntervalCapturing,
    /// 处理中
    Processing,
    /// 停止中
    Stopping,
    /// 错误状态
    Error,
    /// 已取消
    Cancelled,
}

/// 操作命令
#[derive(Debug)]
enum OperationCommand {
    /// 截图
    Screenshot(CaptureConfig, oneshot::Sender<Result<CaptureResult>>),
    /// 录制
    Record(CaptureConfig, oneshot::Sender<Result<CaptureResult>>),
    /// 间隔截图
    IntervalScreenshot(CaptureConfig, oneshot::Sender<Result<CaptureResult>>),
    /// 停止当前操作
    Stop(oneshot::Sender<Result<()>>),
    /// 取消当前操作
    Cancel(oneshot::Sender<Result<()>>),
}

/// 控制器状态
#[derive(Debug)]
struct ControllerState {
    /// 当前操作状态
    operation_state: OperationState,
    /// 当前操作类型
    operation_type: Option<OperationType>,
    /// 当前配置
    current_config: Option<CaptureConfig>,
    /// 权限令牌
    permission_token: Option<PermissionToken>,
    /// 录制会话
    recording_session: Option<RecordingSession>,
    /// 捕获的帧
    captured_frames: Vec<RawFrame>,
    /// 操作开始时间
    start_time: Option<Instant>,
    /// 取消标志
    cancelled: bool,
}

impl Default for ControllerState {
    fn default() -> Self {
        Self {
            operation_state: OperationState::Idle,
            operation_type: None,
            current_config: None,
            permission_token: None,
            recording_session: None,
            captured_frames: Vec::new(),
            start_time: None,
            cancelled: false,
        }
    }
}

/// 屏幕捕获控制器
#[derive(Debug)]
pub struct CaptureController {
    /// 控制器状态
    state: Arc<Mutex<ControllerState>>,
    /// 命令发送器
    command_tx: mpsc::Sender<OperationCommand>,
    /// 后端类型
    backend_type: DisplayServerType,
}

impl CaptureController {
    /// 创建新的控制器
    pub async fn new() -> Result<Self> {
        // 检测显示服务器类型
        let backend_type = detect_display_server();
        
        // 创建状态
        let state = Arc::new(Mutex::new(ControllerState::default()));
        
        // 创建命令通道
        let (command_tx, command_rx) = mpsc::channel(32);
        
        // 启动控制器任务
        let state_clone = Arc::clone(&state);
        tokio::spawn(Self::controller_task(state_clone, command_rx, backend_type));
        
        Ok(Self {
            state,
            command_tx,
            backend_type,
        })
    }
    
    /// 获取当前状态
    pub fn get_state(&self) -> OperationState {
        if let Ok(state) = self.state.lock() {
            state.operation_state
        } else {
            OperationState::Error
        }
    }
    
    /// 获取当前操作类型
    pub fn get_operation_type(&self) -> Option<OperationType> {
        if let Ok(state) = self.state.lock() {
            state.operation_type
        } else {
            None
        }
    }
    
    /// 是否正在执行操作
    pub fn is_busy(&self) -> bool {
        let state = self.get_state();
        state != OperationState::Idle && state != OperationState::Error && state != OperationState::Cancelled
    }
    
    /// 获取操作已运行时间
    pub fn get_elapsed_time(&self) -> Option<Duration> {
        if let Ok(state) = self.state.lock() {
            state.start_time.map(|t| t.elapsed())
        } else {
            None
        }
    }
    
    /// 截图
    pub async fn screenshot(&self, config: CaptureConfig) -> Result<CaptureResult> {
        // 检查是否正在执行其他操作
        if self.is_busy() {
            return Err(CaptureError::UnsupportedOperation("已有正在进行的操作".to_string()));
        }
        
        // 创建响应通道
        let (tx, rx) = oneshot::channel();
        
        // 发送截图命令
        self.command_tx.send(OperationCommand::Screenshot(config, tx)).await
            .map_err(|_| CaptureError::System("无法发送截图命令".to_string()))?;
        
        // 等待响应
        rx.await.map_err(|_| CaptureError::System("无法接收截图结果".to_string()))?
    }
    
    /// 录制
    pub async fn record(&self, config: CaptureConfig) -> Result<CaptureResult> {
        // 检查是否正在执行其他操作
        if self.is_busy() {
            return Err(CaptureError::UnsupportedOperation("已有正在进行的操作".to_string()));
        }
        
        // 创建响应通道
        let (tx, rx) = oneshot::channel();
        
        // 发送录制命令
        self.command_tx.send(OperationCommand::Record(config, tx)).await
            .map_err(|_| CaptureError::System("无法发送录制命令".to_string()))?;
        
        // 等待响应
        rx.await.map_err(|_| CaptureError::System("无法接收录制结果".to_string()))?
    }
    
    /// 间隔截图
    pub async fn interval_screenshot(&self, config: CaptureConfig) -> Result<CaptureResult> {
        // 检查是否正在执行其他操作
        if self.is_busy() {
            return Err(CaptureError::UnsupportedOperation("已有正在进行的操作".to_string()));
        }
        
        // 创建响应通道
        let (tx, rx) = oneshot::channel();
        
        // 发送间隔截图命令
        self.command_tx.send(OperationCommand::IntervalScreenshot(config, tx)).await
            .map_err(|_| CaptureError::System("无法发送间隔截图命令".to_string()))?;
        
        // 等待响应
        rx.await.map_err(|_| CaptureError::System("无法接收间隔截图结果".to_string()))?
    }
    
    /// 停止当前操作
    pub async fn stop(&self) -> Result<()> {
        // 检查是否有正在进行的操作
        if !self.is_busy() {
            return Ok(());
        }
        
        // 创建响应通道
        let (tx, rx) = oneshot::channel();
        
        // 发送停止命令
        self.command_tx.send(OperationCommand::Stop(tx)).await
            .map_err(|_| CaptureError::System("无法发送停止命令".to_string()))?;
        
        // 等待响应
        rx.await.map_err(|_| CaptureError::System("无法接收停止结果".to_string()))?
    }
    
    /// 取消当前操作
    pub async fn cancel(&self) -> Result<()> {
        // 检查是否有正在进行的操作
        if !self.is_busy() {
            return Ok(());
        }
        
        // 创建响应通道
        let (tx, rx) = oneshot::channel();
        
        // 发送取消命令
        self.command_tx.send(OperationCommand::Cancel(tx)).await
            .map_err(|_| CaptureError::System("无法发送取消命令".to_string()))?;
        
        // 等待响应
        rx.await.map_err(|_| CaptureError::System("无法接收取消结果".to_string()))?
    }
    
    /// 控制器任务
    async fn controller_task(
        state: Arc<Mutex<ControllerState>>,
        mut command_rx: mpsc::Receiver<OperationCommand>,
        backend_type: DisplayServerType,
    ) {
        // 创建后端
        let mut backend = match Self::create_backend(backend_type).await {
            Ok(backend) => backend,
            Err(_) => {
                // 如果创建后端失败，尝试使用备用后端
                match Self::create_backend(DisplayServerType::X11).await {
                    Ok(backend) => backend,
                    Err(_) => {
                        // 所有后端都失败，退出任务
                        return;
                    }
                }
            }
        };
        
        // 创建权限管理器
        let mut permission_manager = create_permission_manager();
        
        // 处理命令
        while let Some(command) = command_rx.recv().await {
            match command {
                OperationCommand::Screenshot(config, response_tx) => {
                    // 执行截图
                    let result = Self::handle_screenshot(
                        &state,
                        &mut backend,
                        &mut permission_manager,
                        config,
                    ).await;
                    
                    // 发送结果
                    let _ = response_tx.send(result);
                    
                    // 重置状态
                    if let Ok(mut state) = state.lock() {
                        state.operation_state = OperationState::Idle;
                        state.operation_type = None;
                        state.current_config = None;
                        state.start_time = None;
                        state.cancelled = false;
                    }
                },
                OperationCommand::Record(config, response_tx) => {
                    // 执行录制
                    let result = Self::handle_recording(
                        &state,
                        &mut backend,
                        &mut permission_manager,
                        config,
                    ).await;
                    
                    // 发送结果
                    let _ = response_tx.send(result);
                    
                    // 重置状态
                    if let Ok(mut state) = state.lock() {
                        state.operation_state = OperationState::Idle;
                        state.operation_type = None;
                        state.current_config = None;
                        state.recording_session = None;
                        state.start_time = None;
                        state.cancelled = false;
                    }
                },
                OperationCommand::IntervalScreenshot(config, response_tx) => {
                    // 执行间隔截图
                    let result = Self::handle_interval_screenshot(
                        &state,
                        &mut backend,
                        &mut permission_manager,
                        config,
                    ).await;
                    
                    // 发送结果
                    let _ = response_tx.send(result);
                    
                    // 重置状态
                    if let Ok(mut state) = state.lock() {
                        state.operation_state = OperationState::Idle;
                        state.operation_type = None;
                        state.current_config = None;
                        state.start_time = None;
                        state.cancelled = false;
                    }
                },
                OperationCommand::Stop(response_tx) => {
                    // 停止当前操作
                    if let Ok(mut state) = state.lock() {
                        state.operation_state = OperationState::Stopping;
                    }
                    
                    // 发送成功响应
                    let _ = response_tx.send(Ok(()));
                },
                OperationCommand::Cancel(response_tx) => {
                    // 取消当前操作
                    if let Ok(mut state) = state.lock() {
                        state.cancelled = true;
                        state.operation_state = OperationState::Cancelled;
                    }
                    
                    // 发送成功响应
                    let _ = response_tx.send(Ok(()));
                },
            }
        }
    }
    
    /// 处理截图操作
    async fn handle_screenshot(
        state: &Arc<Mutex<ControllerState>>,
        backend: &mut Box<dyn CaptureBackend>,
        permission_manager: &mut Box<dyn PermissionManager>,
        config: CaptureConfig,
    ) -> Result<CaptureResult> {
        // 记录开始时间
        let start_time = Instant::now();
        
        // 更新状态
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::Initializing;
            state.operation_type = Some(OperationType::Screenshot);
            state.current_config = Some(config.clone());
            state.start_time = Some(start_time);
        }
        
        // 创建文件管理器
        let file_manager = FileManager::with_options(
            &config.output_directory,
            config.filename_prefix.clone(),
            config.overwrite_existing,
        )?;
        
        // 申请权限
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::RequestingPermission;
        }
        
        let token = permission_manager.request_screen_capture_permission().await?;
        
        // 保存权限令牌
        if let Ok(mut state) = state.lock() {
            state.permission_token = Some(token.clone());
            state.operation_state = OperationState::Capturing;
        }
        
        // 检查是否已取消
        if let Ok(state) = state.lock() {
            if state.cancelled {
                return Err(CaptureError::Cancelled);
            }
        }
        
        // 捕获帧
        let frame = backend.capture_frame(&token).await?;
        
        // 检查是否已取消
        if let Ok(state) = state.lock() {
            if state.cancelled {
                return Err(CaptureError::Cancelled);
            }
        }
        
        // 更新状态
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::Processing;
        }
        
        // 保存图像
        let image_format = match config.output_format {
            OutputFormat::PNG => ImageFormat::PNG,
            OutputFormat::JPEG => ImageFormat::JPEG,
            OutputFormat::BMP => ImageFormat::BMP,
            OutputFormat::WEBP => ImageFormat::WEBP,
            _ => ImageFormat::PNG, // 默认使用PNG
        };
        
        let file_path = file_manager.save_frame_as_image_auto(
            &frame,
            image_format,
            config.operation_type,
            config.quality.unwrap_or(90),
        )?;
        
        // 获取文件大小
        let file_size = match file_manager.get_file_size(&file_path) {
            Ok(size) => size,
            Err(e) => {
                // 获取文件大小失败，但不影响整体结果
                tracing::warn!("无法获取文件大小: {}", e);
                0
            }
        };
        
        // 创建结果
        let mut result = CaptureResult::new(config.operation_type);
        result.add_file(file_path, file_size);
        
        // 计算总耗时
        let elapsed = start_time.elapsed();
        result.set_duration(elapsed);
        
        tracing::info!(
            "截图完成: 耗时={:?}, 分辨率={}x{}, 格式={:?}, 文件大小={}字节",
            elapsed,
            frame.width,
            frame.height,
            config.output_format,
            file_size
        );
        
        Ok(result)
    }
    
    /// 处理录制操作
    async fn handle_recording(
        state: &Arc<Mutex<ControllerState>>,
        backend: &mut Box<dyn CaptureBackend>,
        permission_manager: &mut Box<dyn PermissionManager>,
        config: CaptureConfig,
    ) -> Result<CaptureResult> {
        // 记录开始时间
        let start_time = Instant::now();
        
        // 更新状态
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::Initializing;
            state.operation_type = Some(OperationType::Recording);
            state.current_config = Some(config.clone());
            state.start_time = Some(start_time);
        }
        
        // 创建文件管理器
        let file_manager = FileManager::with_options(
            &config.output_directory,
            config.filename_prefix.clone(),
            config.overwrite_existing,
        )?;
        
        // 申请权限
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::RequestingPermission;
        }
        
        let token = permission_manager.request_screen_capture_permission().await?;
        
        // 保存权限令牌
        if let Ok(mut state) = state.lock() {
            state.permission_token = Some(token.clone());
            state.operation_state = OperationState::Preparing;
        }
        
        // 检查是否已取消
        if let Ok(state) = state.lock() {
            if state.cancelled {
                return Err(CaptureError::Cancelled);
            }
        }
        
        // 开始录制会话
        let session = backend.start_recording(&token).await?;
        
        // 保存会话
        if let Ok(mut state) = state.lock() {
            state.recording_session = Some(session.clone());
            state.operation_state = OperationState::Recording;
        }
        
        // 创建监控任务
        let state_clone = Arc::clone(state);
        let duration = config.duration;
        let monitor_handle = tokio::spawn(async move {
            // 如果设置了录制时长，等待指定时间
            if let Some(duration) = duration {
                sleep(duration).await;
                
                // 检查是否仍在录制
                if let Ok(mut state) = state_clone.lock() {
                    if state.operation_state == OperationState::Recording {
                        tracing::info!("录制达到预设时长，准备停止");
                        state.operation_state = OperationState::Stopping;
                    }
                }
            } else {
                // 无限录制，等待停止信号
                loop {
                    sleep(Duration::from_millis(100)).await;
                    
                    if let Ok(state) = state_clone.lock() {
                        if state.cancelled {
                            tracing::info!("收到停止信号，准备停止录制");
                            break;
                        }
                        
                        // 检查是否已取消
                        if state.cancelled {
                            tracing::info!("录制被取消");
                            break;
                        }
                        
                        // 检查是否应该停止
                        if state.operation_state == OperationState::Stopping {
                            tracing::info!("状态变为停止中，准备停止录制");
                            break;
                        }
                    } else {
                        // 无法获取锁，退出
                        break;
                    }
                }
            }
        });
        
        // 等待监控任务完成
        match monitor_handle.await {
            Ok(_) => {},
            Err(e) => {
                tracing::error!("监控任务失败: {}", e);
            }
        }
        
        // 检查是否已取消
        let cancelled = if let Ok(state) = state.lock() {
            state.cancelled
        } else {
            false
        };
        
        // 更新状态
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::Processing;
        }
        
        // 如果已取消，直接返回
        if cancelled {
            return Err(CaptureError::Cancelled);
        }
        
        // 获取录制会话
        let session = if let Ok(state) = state.lock() {
            state.recording_session.clone().ok_or_else(|| {
                CaptureError::Capture("录制会话不存在".to_string())
            })?
        } else {
            return Err(CaptureError::System("无法获取状态锁".to_string()));
        };
        
        // 停止录制
        let video_data = backend.stop_recording(session).await?;
        
        // 保存视频
        let video_format = match config.output_format {
            OutputFormat::MP4 => VideoFormat::MP4,
            OutputFormat::WebM => VideoFormat::WebM,
            OutputFormat::AVI => VideoFormat::AVI,
            OutputFormat::MKV => VideoFormat::MKV,
            _ => VideoFormat::MP4, // 默认使用MP4
        };
        
        let file_path = file_manager.save_video_auto(
            &video_data,
            video_format,
            config.operation_type,
        )?;
        
        // 获取文件大小
        let file_size = match file_manager.get_file_size(&file_path) {
            Ok(size) => size,
            Err(e) => {
                // 获取文件大小失败，但不影响整体结果
                tracing::warn!("无法获取文件大小: {}", e);
                0
            }
        };
        
        // 创建结果
        let mut result = CaptureResult::new(config.operation_type);
        result.add_file(file_path, file_size);
        
        // 计算总耗时
        let elapsed = start_time.elapsed();
        result.set_duration(elapsed);
        
        tracing::info!(
            "录制完成: 耗时={:?}, 格式={:?}, 文件大小={}字节",
            elapsed,
            config.output_format,
            file_size
        );
        
        Ok(result)
    }
    
    /// 处理间隔截图操作
    async fn handle_interval_screenshot(
        state: &Arc<Mutex<ControllerState>>,
        backend: &mut Box<dyn CaptureBackend>,
        permission_manager: &mut Box<dyn PermissionManager>,
        config: CaptureConfig,
    ) -> Result<CaptureResult> {
        // 检查间隔设置
        let interval_duration = config.interval.ok_or_else(|| {
            CaptureError::UnsupportedOperation("间隔截图需要设置间隔时间".to_string())
        })?;
        
        // 记录开始时间
        let start_time = Instant::now();
        
        // 更新状态
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::Initializing;
            state.operation_type = Some(OperationType::IntervalScreenshot);
            state.current_config = Some(config.clone());
            state.start_time = Some(start_time);
        }
        
        // 创建文件管理器
        let file_manager = FileManager::with_options(
            &config.output_directory,
            config.filename_prefix.clone(),
            config.overwrite_existing,
        )?;
        
        // 申请权限
        if let Ok(mut state) = state.lock() {
            state.operation_state = OperationState::RequestingPermission;
        }
        
        let token = permission_manager.request_screen_capture_permission().await?;
        
        // 保存权限令牌
        if let Ok(mut state) = state.lock() {
            state.permission_token = Some(token.clone());
            state.operation_state = OperationState::IntervalCapturing;
        }
        
        // 创建结果
        let mut result = CaptureResult::new(config.operation_type);
        
        // 创建定时器
        let mut interval_timer = interval(interval_duration);
        let mut sequence = 0;
        let mut total_size = 0;
        
        // 如果设置了持续时间，计算结束时间
        let end_time = config.duration.map(|d| start_time + d);
        
        // 循环捕获
        loop {
            // 等待下一个间隔
            interval_timer.tick().await;
            
            // 检查是否应该停止
            if let Ok(state) = state.lock() {
                if state.cancelled {
                    tracing::info!("停止间隔截图操作");
                    break;
                }
                
                if state.operation_state == OperationState::Stopping {
                    tracing::info!("停止间隔截图操作");
                    break;
                }
            }
            
            // 检查是否达到持续时间
            if let Some(end) = end_time {
                if Instant::now() >= end {
                    break;
                }
            }
            
            // 捕获帧
            let frame = match backend.capture_frame(&token).await {
                Ok(frame) => frame,
                Err(e) => {
                    // 捕获失败，记录错误但继续
                    tracing::warn!("间隔截图捕获失败: {}", e);
                    continue;
                }
            };
            
            // 保存图像
            let image_format = match config.output_format {
                OutputFormat::PNG => ImageFormat::PNG,
                OutputFormat::JPEG => ImageFormat::JPEG,
                OutputFormat::BMP => ImageFormat::BMP,
                OutputFormat::WEBP => ImageFormat::WEBP,
                _ => ImageFormat::PNG, // 默认使用PNG
            };
            
            // 生成带序号的文件名
            let filename = file_manager.generate_numbered_filename(
                config.operation_type,
                config.output_format,
                sequence,
            );
            
            // 保存图像
            let file_path = match file_manager.save_frame_as_image(
                &frame,
                image_format,
                &filename,
                config.quality.unwrap_or(90),
            ) {
                Ok(path) => path,
                Err(e) => {
                    // 保存失败，记录错误但继续
                    tracing::warn!("保存间隔截图失败: {}", e);
                    continue;
                }
            };
            
            // 获取文件大小
            let file_size = match file_manager.get_file_size(&file_path) {
                Ok(size) => size,
                Err(_) => 0,
            };
            
            // 添加到结果
            result.add_file(file_path, file_size);
            total_size += file_size;
            
            // 增加序号
            sequence += 1;
        }
        
        // 计算总耗时
        let elapsed = start_time.elapsed();
        result.set_duration(elapsed);
        result.set_total_frames(sequence);
        
        tracing::info!(
            "间隔截图完成: 耗时={:?}, 总帧数={}, 格式={:?}, 总大小={}字节",
            elapsed,
            sequence,
            config.output_format,
            total_size
        );
        
        Ok(result)
    }
    
    /// 创建捕获后端
    async fn create_backend(backend_type: DisplayServerType) -> Result<Box<dyn CaptureBackend>> {
        match backend_type {
            DisplayServerType::Wayland => {
                #[cfg(feature = "wayland")]
                {
                    use crate::backend::pipewire::PipewireCaptureBackend;
                    let backend = PipewireCaptureBackend::new()?;
                    Ok(Box::new(backend) as Box<dyn CaptureBackend>)
                }
                
                #[cfg(not(feature = "wayland"))]
                {
                    Err(CaptureError::UnsupportedOperation("Wayland支持未启用".to_string()))
                }
            },
            DisplayServerType::X11 => {
                #[cfg(feature = "x11")]
                {
                    use crate::backend::x11::X11CaptureBackend;
                    let backend = X11CaptureBackend::new()?;
                    Ok(Box::new(backend) as Box<dyn CaptureBackend>)
                }
                
                #[cfg(not(feature = "x11"))]
                {
                    Err(CaptureError::UnsupportedOperation("X11支持未启用".to_string()))
                }
            },
            DisplayServerType::Unknown => {
                Err(CaptureError::UnsupportedOperation("未知的显示服务器类型".to_string()))
            }
        }
    }
    
    /// 处理错误
    fn handle_error(&self, error: CaptureError) -> Result<()> {
        tracing::error!("操作错误: {}", error);
        
        // 根据错误类型执行不同的恢复操作
        match &error {
            CaptureError::Permission(_) => {
                tracing::warn!("权限错误，可能需要重新申请权限");
            },
            CaptureError::Timeout => {
                tracing::warn!("操作超时，可能需要调整超时设置");
            },
            CaptureError::Cancelled => {
                tracing::info!("操作被用户取消");
            },
            _ => {
                tracing::warn!("尝试从错误中恢复");
            }
        }
        
        // 重置状态
        if let Ok(mut state) = self.state.lock() {
            state.operation_state = OperationState::Error;
            state.cancelled = false;
        }
        
        Err(error)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_controller_creation() {
        let controller = CaptureController::new().await;
        assert!(controller.is_ok());
    }

    #[tokio::test]
    async fn test_controller_state() {
        let controller = CaptureController::new().await.unwrap();
        assert_eq!(controller.get_state(), OperationState::Idle);
        assert_eq!(controller.get_operation_type(), None);
        assert!(!controller.is_busy());
    }

    #[tokio::test]
    async fn test_controller_screenshot_config() {
        let temp_dir = TempDir::new().unwrap();
        let config = CaptureConfig {
            operation_type: OperationType::Screenshot,
            output_directory: temp_dir.path().to_path_buf(),
            output_format: OutputFormat::PNG,
            quality: Some(90),
            duration: None,
            interval: None,
            total_duration: None,
            frame_rate: None,
            filename_prefix: None,
            overwrite_existing: false,
            verbose_logging: false,
        };
        
        // 这个测试只检查配置是否有效，不实际执行截图
        assert_eq!(config.operation_type, OperationType::Screenshot);
        assert_eq!(config.output_format, OutputFormat::PNG);
        assert_eq!(config.quality, Some(90));
    }
}
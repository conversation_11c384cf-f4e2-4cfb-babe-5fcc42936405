//! 视频编码模块

use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use gstreamer as gst;
use gstreamer::prelude::*;
use gstreamer_app as gst_app;
use crate::backend::common::{RawFrame, PixelFormat};
use crate::error::{Result, FileError};
use crate::file_manager::{VideoFormat, FileManager};

/// 视频编码器
#[derive(Debug)]
pub struct VideoEncoder {
    /// 输出格式
    format: VideoFormat,
    /// 帧率
    frame_rate: u32,
    /// 质量（0-100）
    quality: u8,
    /// 是否已初始化
    initialized: bool,
    /// GStreamer管道
    pipeline: Option<gst::Pipeline>,
    /// 应用源
    appsrc: Option<gst_app::AppSrc>,
    /// 文件管理器
    file_manager: Arc<FileManager>,
    /// 输出文件路径
    output_path: PathBuf,
}

impl VideoEncoder {
    /// 创建新的视频编码器
    pub fn new(format: VideoFormat, frame_rate: u32, quality: u8, file_manager: Arc<FileManager>, output_path: PathBuf) -> Self {
        Self {
            format,
            frame_rate,
            quality,
            initialized: false,
            pipeline: None,
            appsrc: None,
            file_manager,
            output_path,
        }
    }
    
    /// 初始化编码器
    pub fn initialize(&mut self, width: u32, height: u32) -> Result<()> {
        if self.initialized {
            return Ok(());
        }
        
        // 初始化GStreamer
        gst::init().map_err(|e| FileError::VideoEncoding(format!("无法初始化GStreamer: {}", e)))?;
        
        // 创建管道
        let pipeline = gst::Pipeline::new(None);
        
        // 创建应用源
        let appsrc = gst_app::AppSrc::builder()
            .format(gst::Format::Time)
            .is_live(true)
            .do_timestamp(true)
            .build();
        
        // 设置应用源属性
        let caps = gst::Caps::builder("video/x-raw")
            .field("format", "RGBA")
            .field("width", width as i32)
            .field("height", height as i32)
            .field("framerate", gst::Fraction::new(self.frame_rate as i32, 1))
            .build();
        
        appsrc.set_caps(Some(&caps));
        appsrc.set_max_bytes(1000000000); // 1GB缓冲区
        
        // 添加应用源到管道
        pipeline.add(&appsrc).map_err(|e| FileError::VideoEncoding(format!("无法添加应用源到管道: {}", e)))?;
        
        // 创建视频转换器
        let videoconvert = gst::ElementFactory::make("videoconvert")
            .name("converter")
            .build()
            .map_err(|e| FileError::VideoEncoding(format!("无法创建视频转换器: {}", e)))?;
        
        // 添加视频转换器到管道
        pipeline.add(&videoconvert).map_err(|e| FileError::VideoEncoding(format!("无法添加视频转换器到管道: {}", e)))?;
        
        // 创建编码器和容器
        let (encoder, muxer) = match self.format {
            VideoFormat::MP4 => {
                // H.264编码器
                let encoder = gst::ElementFactory::make("x264enc")
                    .name("encoder")
                    .property("tune", "zerolatency")
                    .property("speed-preset", "superfast")
                    .property("bitrate", (500 * self.quality as u32) / 100) // 根据质量设置比特率
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建H.264编码器: {}", e)))?;
                
                // MP4容器
                let muxer = gst::ElementFactory::make("mp4mux")
                    .name("muxer")
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建MP4容器: {}", e)))?;
                
                (encoder, muxer)
            },
            VideoFormat::WebM => {
                // VP8编码器
                let encoder = gst::ElementFactory::make("vp8enc")
                    .name("encoder")
                    .property("deadline", 1) // 实时模式
                    .property("target-bitrate", (500000 * self.quality as u32) / 100) // 根据质量设置比特率
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建VP8编码器: {}", e)))?;
                
                // WebM容器
                let muxer = gst::ElementFactory::make("webmmux")
                    .name("muxer")
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建WebM容器: {}", e)))?;
                
                (encoder, muxer)
            },
            VideoFormat::AVI => {
                // MJPEG编码器
                let encoder = gst::ElementFactory::make("jpegenc")
                    .name("encoder")
                    .property("quality", self.quality as i32)
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建MJPEG编码器: {}", e)))?;
                
                // AVI容器
                let muxer = gst::ElementFactory::make("avimux")
                    .name("muxer")
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建AVI容器: {}", e)))?;
                
                (encoder, muxer)
            },
            VideoFormat::MKV => {
                // H.264编码器
                let encoder = gst::ElementFactory::make("x264enc")
                    .name("encoder")
                    .property("tune", "zerolatency")
                    .property("speed-preset", "superfast")
                    .property("bitrate", (500 * self.quality as u32) / 100) // 根据质量设置比特率
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建H.264编码器: {}", e)))?;
                
                // MKV容器
                let muxer = gst::ElementFactory::make("matroskamux")
                    .name("muxer")
                    .build()
                    .map_err(|e| FileError::VideoEncoding(format!("无法创建MKV容器: {}", e)))?;
                
                (encoder, muxer)
            },
        };
        
        // 添加编码器和容器到管道
        pipeline.add(&encoder).map_err(|e| FileError::VideoEncoding(format!("无法添加编码器到管道: {}", e)))?;
        pipeline.add(&muxer).map_err(|e| FileError::VideoEncoding(format!("无法添加容器到管道: {}", e)))?;
        
        // 创建文件接收器
        let filesink = gst::ElementFactory::make("filesink")
            .name("filesink")
            .property("location", self.output_path.to_str().unwrap())
            .property("sync", false)
            .property("async", false)
            .build()
            .map_err(|e| FileError::VideoEncoding(format!("无法创建文件接收器: {}", e)))?;
        
        // 添加文件接收器到管道
        pipeline.add(&filesink).map_err(|e| FileError::VideoEncoding(format!("无法添加文件接收器到管道: {}", e)))?;
        
        // 连接元素
        appsrc.link(&videoconvert).map_err(|e| FileError::VideoEncoding(format!("无法连接应用源和视频转换器: {}", e)))?;
        videoconvert.link(&encoder).map_err(|e| FileError::VideoEncoding(format!("无法连接视频转换器和编码器: {}", e)))?;
        encoder.link(&muxer).map_err(|e| FileError::VideoEncoding(format!("无法连接编码器和容器: {}", e)))?;
        muxer.link(&filesink).map_err(|e| FileError::VideoEncoding(format!("无法连接容器和文件接收器: {}", e)))?;
        
        // 设置管道状态为播放
        pipeline.set_state(gst::State::Playing).map_err(|e| FileError::VideoEncoding(format!("无法设置管道状态为播放: {}", e)))?;
        
        // 保存管道和应用源
        self.pipeline = Some(pipeline);
        self.appsrc = Some(appsrc);
        self.initialized = true;
        
        Ok(())
    }
    
    /// 添加帧
    pub fn add_frame(&mut self, frame: &RawFrame) -> Result<()> {
        // 如果未初始化，尝试初始化
        if !self.initialized {
            match self.initialize(frame.width, frame.height) {
                Ok(_) => {},
                Err(e) => {
                    // 初始化失败，尝试回退到更简单的编码器
                    log::warn!("初始化编码器失败: {}，尝试回退到更简单的编码器", e);
                    self.fallback_initialize(frame.width, frame.height)?;
                }
            }
        }
        
        if let Some(appsrc) = &self.appsrc {
            // 创建缓冲区
            let mut buffer = match gst::Buffer::with_size(frame.data.len()) {
                Ok(buffer) => buffer,
                Err(e) => {
                    // 创建缓冲区失败，尝试使用更小的缓冲区
                    log::warn!("创建缓冲区失败: {}，尝试使用更小的缓冲区", e);
                    return self.add_frame_with_smaller_buffer(frame);
                }
            };
            
            // 写入数据
            let buffer = buffer.make_mut();
            if let Err(e) = buffer.copy_from_slice(0, &frame.data) {
                log::error!("无法写入数据到缓冲区: {}", e);
                return Err(FileError::VideoEncoding(format!("无法写入数据到缓冲区: {}", e)).into());
            }
            
            // 推送缓冲区
            let buffer_clone = buffer.copy_deep().map_err(|e| {
                FileError::VideoEncoding(format!("无法复制缓冲区: {}", e))
            })?;
            if let Err(e) = appsrc.push_buffer(buffer_clone) {
                log::error!("无法推送缓冲区: {}", e);
                
                // 检查错误类型，如果是流错误，尝试重新初始化
                if e.to_string().contains("flow") {
                    log::warn!("流错误，尝试重新初始化编码器");
                    self.reset()?;
                    self.initialize(frame.width, frame.height)?;
                    return self.add_frame(frame);
                }
                
                return Err(FileError::VideoEncoding(format!("无法推送缓冲区: {}", e)).into());
            }
        }
        
        Ok(())
    }
    
    /// 使用更小的缓冲区添加帧
    fn add_frame_with_smaller_buffer(&mut self, frame: &RawFrame) -> Result<()> {
        if let Some(appsrc) = &self.appsrc {
            // 计算每个块的大小
            let chunk_size = 1024 * 1024; // 1MB
            let total_size = frame.data.len();
            let chunks = (total_size + chunk_size - 1) / chunk_size;
            
            for i in 0..chunks {
                let start = i * chunk_size;
                let end = std::cmp::min((i + 1) * chunk_size, total_size);
                let chunk = &frame.data[start..end];
                
                // 创建缓冲区
                let mut buffer = gst::Buffer::with_size(chunk.len()).map_err(|e| {
                    FileError::VideoEncoding(format!("无法创建缓冲区: {}", e))
                })?;
                
                // 写入数据
                let buffer = buffer.make_mut();
                buffer.copy_from_slice(0, chunk).map_err(|e| {
                    FileError::VideoEncoding(format!("无法写入数据到缓冲区: {}", e))
                })?;
                
                // 推送缓冲区
                let buffer_clone = buffer.copy_deep().map_err(|e| {
                    FileError::VideoEncoding(format!("无法复制缓冲区: {}", e))
                })?;
                appsrc.push_buffer(buffer_clone).map_err(|e| {
                    FileError::VideoEncoding(format!("无法推送缓冲区: {}", e))
                })?;
            }
        }
        
        Ok(())
    }
    
    /// 回退初始化（使用更简单的编码器）
    fn fallback_initialize(&mut self, width: u32, height: u32) -> Result<()> {
        if self.initialized {
            return Ok(());
        }
        
        // 初始化GStreamer
        if let Err(e) = gst::init() {
            return Err(FileError::VideoEncoding(format!("无法初始化GStreamer: {}", e)).into());
        }
        
        // 创建管道
        let pipeline = gst::Pipeline::new(None);
        
        // 创建应用源
        let appsrc = gst_app::AppSrc::builder()
            .format(gst::Format::Time)
            .is_live(true)
            .do_timestamp(true)
            .build();
        
        // 设置应用源属性
        let caps = gst::Caps::builder("video/x-raw")
            .field("format", "RGBA")
            .field("width", width as i32)
            .field("height", height as i32)
            .field("framerate", gst::Fraction::new(self.frame_rate as i32, 1))
            .build();
        
        appsrc.set_caps(Some(&caps));
        appsrc.set_max_bytes(1000000000); // 1GB缓冲区
        
        // 添加应用源到管道
        if let Err(e) = pipeline.add(&appsrc) {
            return Err(FileError::VideoEncoding(format!("无法添加应用源到管道: {}", e)).into());
        }
        
        // 创建视频转换器
        let videoconvert = match gst::ElementFactory::make("videoconvert")
            .name("converter")
            .build() {
            Ok(element) => element,
            Err(e) => return Err(FileError::VideoEncoding(format!("无法创建视频转换器: {}", e)).into()),
        };
        
        // 添加视频转换器到管道
        if let Err(e) = pipeline.add(&videoconvert) {
            return Err(FileError::VideoEncoding(format!("无法添加视频转换器到管道: {}", e)).into());
        }
        
        // 使用最简单的编码器和容器
        let encoder = match gst::ElementFactory::make("jpegenc")
            .name("encoder")
            .property("quality", 50) // 中等质量
            .build() {
            Ok(element) => element,
            Err(e) => return Err(FileError::VideoEncoding(format!("无法创建JPEG编码器: {}", e)).into()),
        };
        
        let muxer = match gst::ElementFactory::make("avimux")
            .name("muxer")
            .build() {
            Ok(element) => element,
            Err(e) => return Err(FileError::VideoEncoding(format!("无法创建AVI容器: {}", e)).into()),
        };
        
        // 添加编码器和容器到管道
        if let Err(e) = pipeline.add(&encoder) {
            return Err(FileError::VideoEncoding(format!("无法添加编码器到管道: {}", e)).into());
        }
        
        if let Err(e) = pipeline.add(&muxer) {
            return Err(FileError::VideoEncoding(format!("无法添加容器到管道: {}", e)).into());
        }
        
        // 创建文件接收器
        let filesink = match gst::ElementFactory::make("filesink")
            .name("filesink")
            .property("location", self.output_path.to_str().unwrap())
            .property("sync", false)
            .property("async", false)
            .build() {
            Ok(element) => element,
            Err(e) => return Err(FileError::VideoEncoding(format!("无法创建文件接收器: {}", e)).into()),
        };
        
        // 添加文件接收器到管道
        if let Err(e) = pipeline.add(&filesink) {
            return Err(FileError::VideoEncoding(format!("无法添加文件接收器到管道: {}", e)).into());
        }
        
        // 连接元素
        if let Err(e) = appsrc.link(&videoconvert) {
            return Err(FileError::VideoEncoding(format!("无法连接应用源和视频转换器: {}", e)).into());
        }
        
        if let Err(e) = videoconvert.link(&encoder) {
            return Err(FileError::VideoEncoding(format!("无法连接视频转换器和编码器: {}", e)).into());
        }
        
        if let Err(e) = encoder.link(&muxer) {
            return Err(FileError::VideoEncoding(format!("无法连接编码器和容器: {}", e)).into());
        }
        
        if let Err(e) = muxer.link(&filesink) {
            return Err(FileError::VideoEncoding(format!("无法连接容器和文件接收器: {}", e)).into());
        }
        
        // 设置管道状态为播放
        if let Err(e) = pipeline.set_state(gst::State::Playing) {
            return Err(FileError::VideoEncoding(format!("无法设置管道状态为播放: {}", e)).into());
        }
        
        // 保存管道和应用源
        self.pipeline = Some(pipeline);
        self.appsrc = Some(appsrc);
        self.initialized = true;
        
        Ok(())
    }
    
    /// 重置编码器
    fn reset(&mut self) -> Result<()> {
        if let Some(pipeline) = &self.pipeline {
            // 设置管道状态为空闲
            if let Err(e) = pipeline.set_state(gst::State::Null) {
                log::warn!("无法设置管道状态为空闲: {}", e);
            }
        }
        
        // 重置状态
        self.initialized = false;
        self.pipeline = None;
        self.appsrc = None;
        
        Ok(())
    }
    
    /// 完成编码
    pub fn finalize(&mut self) -> Result<PathBuf> {
        if let Some(appsrc) = &self.appsrc {
            // 发送EOS
            appsrc.end_of_stream().map_err(|e| {
                FileError::VideoEncoding(format!("无法发送EOS: {}", e))
            })?;
        }
        
        if let Some(pipeline) = &self.pipeline {
            // 等待EOS
            let bus = pipeline.bus().unwrap();
            let _eos = bus.timed_pop_filtered(gst::ClockTime::NONE, &[gst::MessageType::Eos]);
            
            // 设置管道状态为空闲
            pipeline.set_state(gst::State::Null).map_err(|e| {
                FileError::VideoEncoding(format!("无法设置管道状态为空闲: {}", e))
            })?;
        }
        
        // 重置状态
        self.initialized = false;
        self.pipeline = None;
        self.appsrc = None;
        
        Ok(self.output_path.clone())
    }
}

/// 视频编码器构建器
#[derive(Debug)]
pub struct VideoEncoderBuilder {
    format: VideoFormat,
    frame_rate: u32,
    quality: u8,
    file_manager: Arc<FileManager>,
    output_path: Option<PathBuf>,
}

impl VideoEncoderBuilder {
    /// 创建新的视频编码器构建器
    pub fn new(file_manager: Arc<FileManager>) -> Self {
        Self {
            format: VideoFormat::MP4,
            frame_rate: 30,
            quality: 80,
            file_manager,
            output_path: None,
        }
    }
    
    /// 设置格式
    pub fn format(mut self, format: VideoFormat) -> Self {
        self.format = format;
        self
    }
    
    /// 设置帧率
    pub fn frame_rate(mut self, frame_rate: u32) -> Self {
        self.frame_rate = frame_rate;
        self
    }
    
    /// 设置质量
    pub fn quality(mut self, quality: u8) -> Self {
        self.quality = quality.clamp(1, 100);
        self
    }
    
    /// 设置输出路径
    pub fn output_path<P: AsRef<Path>>(mut self, path: P) -> Self {
        self.output_path = Some(path.as_ref().to_path_buf());
        self
    }
    
    /// 构建视频编码器
    pub fn build(self) -> Result<VideoEncoder> {
        // 如果没有指定输出路径，则生成一个临时文件名
        let output_path = if let Some(path) = self.output_path {
            path
        } else {
            let filename = format!("recording_{}.{}", 
                chrono::Local::now().format("%Y%m%d_%H%M%S"),
                match self.format {
                    VideoFormat::MP4 => "mp4",
                    VideoFormat::WebM => "webm",
                    VideoFormat::AVI => "avi",
                    VideoFormat::MKV => "mkv",
                }
            );
            self.file_manager.get_output_directory().join(filename)
        };
        
        Ok(VideoEncoder::new(
            self.format,
            self.frame_rate,
            self.quality,
            self.file_manager,
            output_path,
        ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use crate::file_manager::FileManager;
    
    #[test]
    fn test_video_encoder_builder() {
        // 初始化GStreamer
        gst::init().unwrap();
        
        let temp_dir = TempDir::new().unwrap();
        let file_manager = Arc::new(FileManager::new(temp_dir.path()).unwrap());
        
        let builder = VideoEncoderBuilder::new(file_manager.clone())
            .format(VideoFormat::MP4)
            .frame_rate(30)
            .quality(80);
        
        let encoder = builder.build();
        assert!(encoder.is_ok());
    }
}
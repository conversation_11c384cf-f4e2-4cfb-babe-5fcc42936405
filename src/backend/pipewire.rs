//! PipeWire捕获后端实现

use async_trait::async_trait;
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime};
use tokio::sync::oneshot;
use tokio::time::timeout;
use pipewire::{
    self,
    context::Context,
    core::Core,
    main_loop::MainLoop,
    properties::Properties,
    spa::{self, buffer::Buffer, param::ParamType, pod::Pod},
    stream::{Stream, StreamFlags, StreamListener},
};
use crate::permission::PermissionToken;
use crate::error::{CaptureError, Result};
use super::common::{CaptureBackend, PixelFormat, RawFrame, RecordingSession};

/// PipeWire捕获后端
#[derive(Debug)]
pub struct PipeWireCaptureBackend {
    /// PipeWire节点ID
    node_id: Option<u32>,
    /// 是否已初始化
    is_initialized: bool,
    /// 主循环
    main_loop: Option<MainLoop>,
    /// 上下文
    context: Option<Context>,
    /// 核心
    core: Option<Core>,
    /// 流
    stream: Option<Stream>,
    /// 最新帧
    latest_frame: Arc<Mutex<Option<RawFrame>>>,
    /// 录制会话
    recording_session: Option<RecordingSession>,
    /// 录制帧缓冲
    recording_frames: Arc<Mutex<Vec<RawFrame>>>,
    /// 是否为GNOME环境
    is_gnome: bool,
}

impl PipeWireCaptureBackend {
    /// 创建新的PipeWire后端
    pub fn new() -> Self {
        // 初始化PipeWire
        pipewire::init();
        
        Self {
            node_id: None,
            is_initialized: false,
            main_loop: None,
            context: None,
            core: None,
            stream: None,
            latest_frame: Arc::new(Mutex::new(None)),
            recording_session: None,
            recording_frames: Arc::new(Mutex::new(Vec::new())),
            is_gnome: Self::detect_gnome_environment(),
        }
    }
    
    /// 检测是否为GNOME环境
    fn detect_gnome_environment() -> bool {
        if let Ok(desktop) = std::env::var("XDG_CURRENT_DESKTOP") {
            return desktop.to_lowercase().contains("gnome");
        }
        false
    }
    
    /// 初始化PipeWire连接
    pub fn initialize(&mut self) -> Result<()> {
        if self.is_initialized {
            return Ok(());
        }
        
        // 创建主循环
        let main_loop = MainLoop::new(None)?;
        
        // 创建上下文
        let context = Context::new(&main_loop)?;
        
        // 创建核心
        let core = context.connect(None)?;
        
        self.main_loop = Some(main_loop);
        self.context = Some(context);
        self.core = Some(core);
        self.is_initialized = true;
        
        Ok(())
    }
    
    /// 创建PipeWire流
    fn create_stream(&mut self, node_id: u32) -> Result<()> {
        if self.stream.is_some() {
            return Ok(());
        }
        
        let main_loop = self.main_loop.as_ref().unwrap();
        
        // 创建流属性
        let mut props = Properties::new();
        props.set("media.class", "Video/Source");
        props.set("node.name", "screen-capture");
        
        // 创建流
        let mut stream = Stream::new(
            &self.core.as_ref().unwrap(),
            "screen-capture",
            props,
        )?;
        
        // 设置流参数
        let mut params = Vec::new();
        
        // 添加视频格式参数
        let param = spa::pod::builder::Pod::builder()
            .object(
                spa::pod::object::Object::builder(
                    spa::utils::SpaTypes::ObjectParamFormat.as_raw(),
                    spa::param::ParamType::EnumFormat.as_raw(),
                )
                .prop(
                    spa::param::format::FormatProperties::MediaType.as_raw(),
                    &spa::pod::property::Property::new(
                        spa::param::format::FormatProperties::MediaType.as_raw(),
                        spa::pod::Value::Id(spa::param::format::MediaType::Video.as_raw()),
                    ),
                )
                .prop(
                    spa::param::format::FormatProperties::MediaSubtype.as_raw(),
                    &spa::pod::property::Property::new(
                        spa::param::format::FormatProperties::MediaSubtype.as_raw(),
                        spa::pod::Value::Id(spa::param::format::MediaSubtype::Raw.as_raw()),
                    ),
                )
                .prop(
                    spa::param::format::FormatProperties::VideoFormat.as_raw(),
                    &spa::pod::property::Property::new(
                        spa::param::format::FormatProperties::VideoFormat.as_raw(),
                        spa::pod::Value::Choice(spa::pod::choice::Choice::Enum(
                            spa::pod::choice::EnumChoice::new(
                                spa::pod::Value::Id(spa::param::format::VideoFormat::BGRA.as_raw()),
                                vec![
                                    spa::pod::Value::Id(spa::param::format::VideoFormat::BGRA.as_raw()),
                                    spa::pod::Value::Id(spa::param::format::VideoFormat::RGBA.as_raw()),
                                    spa::pod::Value::Id(spa::param::format::VideoFormat::BGRx.as_raw()),
                                    spa::pod::Value::Id(spa::param::format::VideoFormat::RGBx.as_raw()),
                                ],
                            ),
                        )),
                    ),
                )
                .build()
            )
            .build();
        
        params.push(param);
        
        // 设置流监听器
        let latest_frame = self.latest_frame.clone();
        let recording_frames = self.recording_frames.clone();
        let recording_active = Arc::new(Mutex::new(false));
        
        let recording_active_clone = recording_active.clone();
        let mut listener = stream.add_listener();
        
        listener
            .process(move |stream| {
                // 处理流数据
                if let Ok(buffer) = stream.dequeue_buffer() {
                    if let Some(datas) = buffer.datas() {
                        if let Some(data) = datas.get(0) {
                            if let Some(chunk) = data.chunk() {
                                if chunk.size() > 0 {
                                    // 获取元数据
                                    let mut width = 0;
                                    let mut height = 0;
                                    let mut stride = 0;
                                    let mut format = PixelFormat::BGRA8;
                                    
                                    if let Some(meta) = buffer.get_meta::<pipewire::spa::meta::VideoCrop>() {
                                        width = meta.width();
                                        height = meta.height();
                                    }
                                    
                                    if let Some(meta) = buffer.get_meta::<pipewire::spa::meta::VideoInfo>() {
                                        stride = meta.stride();
                                        
                                        // 确定像素格式
                                        match meta.format() {
                                            pipewire::spa::param::format::VideoFormat::BGRA => {
                                                format = PixelFormat::BGRA8;
                                            }
                                            pipewire::spa::param::format::VideoFormat::RGBA => {
                                                format = PixelFormat::RGBA8;
                                            }
                                            _ => {
                                                // 默认使用BGRA8
                                                format = PixelFormat::BGRA8;
                                            }
                                        }
                                    }
                                    
                                    // 如果没有获取到元数据，使用默认值
                                    if width == 0 || height == 0 || stride == 0 {
                                        width = 1920;
                                        height = 1080;
                                        stride = width * 4;
                                    }
                                    
                                    // 复制帧数据
                                    let data_slice = unsafe {
                                        std::slice::from_raw_parts(
                                            chunk.data() as *const u8,
                                            chunk.size() as usize,
                                        )
                                    };
                                    
                                    let frame_data = data_slice.to_vec();
                                    
                                    // 创建帧
                                    let frame = RawFrame::new(
                                        frame_data,
                                        width,
                                        height,
                                        stride,
                                        format,
                                    );
                                    
                                    // 更新最新帧
                                    if let Ok(mut latest) = latest_frame.lock() {
                                        *latest = Some(frame.clone());
                                    }
                                    
                                    // 如果正在录制，添加到录制帧缓冲
                                    if let Ok(is_recording) = recording_active_clone.lock() {
                                        if *is_recording {
                                            if let Ok(mut frames) = recording_frames.lock() {
                                                frames.push(frame);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 将缓冲区放回队列
                    stream.queue_buffer(buffer);
                }
            });
        
        // 连接到节点
        stream.connect(
            pipewire::spa::Direction::Input,
            Some(node_id),
            StreamFlags::AUTOCONNECT | StreamFlags::MAP_BUFFERS,
            &params,
        )?;
        
        self.stream = Some(stream);
        self.node_id = Some(node_id);
        
        Ok(())
    }
    
    /// 启动PipeWire主循环
    fn start_main_loop(&self) -> Result<()> {
        if let Some(main_loop) = &self.main_loop {
            // 在单独的线程中运行主循环
            let main_loop_clone = main_loop.clone();
            std::thread::spawn(move || {
                main_loop_clone.run();
            });
        }
        
        Ok(())
    }
    
    /// 等待帧可用
    async fn wait_for_frame(&self, timeout_secs: u64) -> Result<RawFrame> {
        let (tx, rx) = oneshot::channel();
        let latest_frame = self.latest_frame.clone();
        
        // 在单独的任务中检查帧
        tokio::spawn(async move {
            let mut attempts = 0;
            loop {
                if let Ok(frame) = latest_frame.lock() {
                    if let Some(frame) = frame.clone() {
                        let _ = tx.send(frame);
                        break;
                    }
                }
                
                attempts += 1;
                if attempts > 100 {
                    // 超过100次尝试，发送错误
                    break;
                }
                
                // 等待10毫秒再次检查
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
        });
        
        // 等待帧或超时
        match timeout(Duration::from_secs(timeout_secs), rx).await {
            Ok(Ok(frame)) => Ok(frame),
            Ok(Err(_)) => Err(CaptureError::Capture("帧接收通道已关闭".to_string())),
            Err(_) => Err(CaptureError::Timeout),
        }
    }
}

impl Default for PipeWireCaptureBackend {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl CaptureBackend for PipeWireCaptureBackend {
    async fn capture_frame(&mut self, token: &PermissionToken) -> Result<RawFrame> {
        if !self.is_initialized {
            self.initialize()?;
        }
        
        // 获取节点ID
        let node_id = match token.node_id {
            Some(id) => id,
            None => return Err(CaptureError::Capture("无效的PipeWire节点ID".to_string())),
        };
        
        // 创建流（如果尚未创建）
        if self.stream.is_none() {
            self.create_stream(node_id)?;
            self.start_main_loop()?;
            
            // 等待流初始化
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        // 等待帧可用（最多等待2秒）
        let frame = self.wait_for_frame(2).await?;
        
        Ok(frame)
    }
    
    async fn start_recording(&mut self, token: &PermissionToken) -> Result<RecordingSession> {
        if !self.is_initialized {
            self.initialize()?;
        }
        
        // 获取节点ID
        let node_id = match token.node_id {
            Some(id) => id,
            None => return Err(CaptureError::Capture("无效的PipeWire节点ID".to_string())),
        };
        
        // 创建流（如果尚未创建）
        if self.stream.is_none() {
            self.create_stream(node_id)?;
            self.start_main_loop()?;
            
            // 等待流初始化
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        // 清空录制帧缓冲
        if let Ok(mut frames) = self.recording_frames.lock() {
            frames.clear();
        }
        
        // 创建录制会话
        let session_id = format!("pw_recording_{}", uuid::Uuid::new_v4());
        let session = RecordingSession::new(
            session_id,
            30, // 默认30fps
            (1920, 1080), // 默认分辨率，将在捕获第一帧时更新
        );
        
        self.recording_session = Some(session.clone());
        
        Ok(session)
    }
    
    async fn stop_recording(&mut self, session: RecordingSession) -> Result<Vec<u8>> {
        // 检查会话是否匹配
        if let Some(current_session) = &self.recording_session {
            if current_session.session_id != session.session_id {
                return Err(CaptureError::Capture("录制会话不匹配".to_string()));
            }
        } else {
            return Err(CaptureError::Capture("没有活动的录制会话".to_string()));
        }
        
        // 获取录制的帧
        let frames = if let Ok(frames) = self.recording_frames.lock() {
            frames.clone()
        } else {
            return Err(CaptureError::Capture("无法访问录制帧".to_string()));
        };
        
        if frames.is_empty() {
            return Err(CaptureError::Capture("没有捕获到帧".to_string()));
        }
        
        // 清理录制状态
        self.recording_session = None;
        if let Ok(mut frames_buffer) = self.recording_frames.lock() {
            frames_buffer.clear();
        }
        
        // 这里应该实现视频编码
        // 暂时返回第一帧的数据作为示例
        Ok(frames[0].data.clone())
    }
    
    fn is_available(&self) -> bool {
        // 检查PipeWire是否可用
        // 简单检查环境变量
        std::env::var("WAYLAND_DISPLAY").is_ok()
    }
    
    fn supported_formats(&self) -> Vec<PixelFormat> {
        vec![PixelFormat::RGBA8, PixelFormat::BGRA8]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pipewire_backend_creation() {
        let backend = PipeWireCaptureBackend::new();
        // 只是测试创建不会崩溃
        assert!(!backend.is_initialized);
    }
}
//! 通用后端接口和数据结构

use std::time::SystemTime;
use async_trait::async_trait;
use crate::permission::PermissionToken;
use crate::error::{CaptureError, Result};

/// 像素格式
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PixelFormat {
    RGBA8,
    BGRA8,
    RGB8,
    BGR8,
}

/// 原始帧数据
#[derive(Debu<PERSON>, Clone)]
pub struct RawFrame {
    /// 图像数据
    pub data: Vec<u8>,
    /// 宽度
    pub width: u32,
    /// 高度
    pub height: u32,
    /// 行跨度
    pub stride: u32,
    /// 像素格式
    pub format: PixelFormat,
    /// 时间戳
    pub timestamp: SystemTime,
}

impl RawFrame {
    /// 创建新的帧
    pub fn new(
        data: Vec<u8>,
        width: u32,
        height: u32,
        stride: u32,
        format: PixelFormat,
    ) -> Self {
        Self {
            data,
            width,
            height,
            stride,
            format,
            timestamp: SystemTime::now(),
        }
    }
    
    /// 获取帧的字节大小
    pub fn size(&self) -> usize {
        self.data.len()
    }
    
    /// 检查帧数据是否有效
    pub fn is_valid(&self) -> bool {
        !self.data.is_empty() && self.width > 0 && self.height > 0
    }
}

/// 录制会话
#[derive(Debug, Clone)]
pub struct RecordingSession {
    /// 会话ID
    pub session_id: String,
    /// 开始时间
    pub start_time: SystemTime,
    /// 帧率
    pub frame_rate: u32,
    /// 分辨率
    pub resolution: (u32, u32),
}

impl RecordingSession {
    /// 创建新的录制会话
    pub fn new(session_id: String, frame_rate: u32, resolution: (u32, u32)) -> Self {
        Self {
            session_id,
            start_time: SystemTime::now(),
            frame_rate,
            resolution,
        }
    }
    
    /// 获取录制持续时间
    pub fn duration(&self) -> std::time::Duration {
        SystemTime::now()
            .duration_since(self.start_time)
            .unwrap_or_default()
    }
}

/// 捕获后端trait
#[async_trait]
pub trait CaptureBackend: Send + Sync + std::fmt::Debug {
    /// 捕获单帧
    async fn capture_frame(&mut self, token: &PermissionToken) -> Result<RawFrame>;
    
    /// 开始录制
    async fn start_recording(&mut self, token: &PermissionToken) -> Result<RecordingSession>;
    
    /// 停止录制
    async fn stop_recording(&mut self, session: RecordingSession) -> Result<Vec<u8>>;
    
    /// 检查后端是否可用
    fn is_available(&self) -> bool;
    
    /// 获取支持的像素格式
    fn supported_formats(&self) -> Vec<PixelFormat>;
}

/// 模拟捕获后端（用于测试）
#[derive(Debug)]
pub struct MockCaptureBackend {
    frames: Vec<RawFrame>,
    current_frame: usize,
}

impl MockCaptureBackend {
    /// 创建新的模拟后端
    pub fn new() -> Self {
        Self {
            frames: Vec::new(),
            current_frame: 0,
        }
    }
    
    /// 添加模拟帧
    pub fn add_frame(&mut self, frame: RawFrame) {
        self.frames.push(frame);
    }
    
    /// 创建测试帧
    pub fn create_test_frame(width: u32, height: u32) -> RawFrame {
        let size = (width * height * 4) as usize; // RGBA
        let data = vec![128u8; size]; // 灰色填充
        
        RawFrame::new(data, width, height, width * 4, PixelFormat::RGBA8)
    }
}

impl Default for MockCaptureBackend {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl CaptureBackend for MockCaptureBackend {
    async fn capture_frame(&mut self, _token: &PermissionToken) -> Result<RawFrame> {
        if self.frames.is_empty() {
            // 生成默认测试帧
            return Ok(Self::create_test_frame(1920, 1080));
        }
        
        let frame = self.frames[self.current_frame % self.frames.len()].clone();
        self.current_frame += 1;
        Ok(frame)
    }
    
    async fn start_recording(&mut self, _token: &PermissionToken) -> Result<RecordingSession> {
        Ok(RecordingSession::new(
            "mock_session".to_string(),
            30,
            (1920, 1080),
        ))
    }
    
    async fn stop_recording(&mut self, _session: RecordingSession) -> Result<Vec<u8>> {
        // 返回模拟的视频数据
        Ok(vec![0u8; 1024]) // 1KB的模拟数据
    }
    
    fn is_available(&self) -> bool {
        true
    }
    
    fn supported_formats(&self) -> Vec<PixelFormat> {
        vec![PixelFormat::RGBA8, PixelFormat::BGRA8, PixelFormat::RGB8, PixelFormat::BGR8]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_raw_frame_creation() {
        let frame = RawFrame::new(
            vec![255u8; 1920 * 1080 * 4],
            1920,
            1080,
            1920 * 4,
            PixelFormat::RGBA8,
        );
        
        assert!(frame.is_valid());
        assert_eq!(frame.width, 1920);
        assert_eq!(frame.height, 1080);
    }

    #[test]
    fn test_recording_session() {
        let session = RecordingSession::new("test".to_string(), 30, (1920, 1080));
        assert_eq!(session.session_id, "test");
        assert_eq!(session.frame_rate, 30);
        assert_eq!(session.resolution, (1920, 1080));
    }

    #[test]
    fn test_mock_backend() {
        let mut backend = MockCaptureBackend::new();
        assert!(backend.is_available());
        assert!(!backend.supported_formats().is_empty());
    }
}
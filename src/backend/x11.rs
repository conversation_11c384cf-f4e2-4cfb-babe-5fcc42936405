//! X11捕获后端实现

use async_trait::async_trait;
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime};
use tokio::sync::oneshot;
use tokio::time::timeout;
use x11rb::connection::Connection;
use x11rb::protocol::xproto::{self, ConnectionExt, ImageFormat, ImageOrder, Screen};
use x11rb::wrapper::ConnectionExt as _;
use x11rb::xcb_ffi::XCBConnection;
use crate::permission::PermissionToken;
use crate::error::{CaptureError, Result};
use super::common::{CaptureBackend, PixelFormat, RawFrame, RecordingSession};

/// X11捕获后端
#[derive(Debug)]
pub struct X11CaptureBackend {
    /// 是否已初始化
    is_initialized: bool,
    /// 显示名称
    display_name: Option<String>,
    /// X11连接
    connection: Option<Arc<XCBConnection>>,
    /// 屏幕信息
    screen: Option<Screen>,
    /// 根窗口ID
    root_window: Option<xproto::Window>,
    /// 最新帧
    latest_frame: Arc<Mutex<Option<RawFrame>>>,
    /// 录制会话
    recording_session: Option<RecordingSession>,
    /// 录制帧缓冲
    recording_frames: Arc<Mutex<Vec<RawFrame>>>,
    /// 是否正在录制
    is_recording: Arc<Mutex<bool>>,
    /// 录制线程句柄
    recording_handle: Option<tokio::task::JoinHandle<()>>,
}

impl X11CaptureBackend {
    /// 创建新的X11后端
    pub fn new() -> Result<Self> {
        // 检查X11环境
        let display = match std::env::var("DISPLAY") {
            Ok(val) => Some(val),
            Err(_) => None,
        };
        
        // 如果没有DISPLAY环境变量，返回错误
        if display.is_none() {
            return Err(crate::error::CaptureError::Backend("未检测到X11环境".to_string()));
        }
        
        Ok(Self {
            is_initialized: false,
            display_name: display,
            connection: None,
            screen: None,
            root_window: None,
            latest_frame: Arc::new(Mutex::new(None)),
            recording_session: None,
            recording_frames: Arc::new(Mutex::new(Vec::new())),
            is_recording: Arc::new(Mutex::new(false)),
            recording_handle: None,
        })
    }
    
    /// 初始化X11连接
    pub fn initialize(&mut self) -> Result<()> {
        if self.is_initialized {
            return Ok(());
        }
        
        // 创建X11连接
        let (conn, screen_num) = match XCBConnection::connect(None) {
            Ok(conn) => conn,
            Err(err) => {
                return Err(CaptureError::Backend(format!("无法连接到X11服务器: {}", err)));
            }
        };
        
        // 获取屏幕信息
        let setup = conn.setup();
        let screen = setup.roots.get(screen_num).ok_or_else(|| {
            CaptureError::Backend(format!("无法获取屏幕信息: {}", screen_num))
        })?.clone();
        
        // 保存连接和屏幕信息
        self.connection = Some(Arc::new(conn));
        self.screen = Some(screen.clone());
        self.root_window = Some(screen.root);
        self.is_initialized = true;
        
        Ok(())
    }
    
    /// 使用X11 API捕获屏幕
    async fn capture_screen(&mut self) -> Result<RawFrame> {
        let conn = self.connection.as_ref().ok_or_else(|| {
            CaptureError::Backend("X11连接未初始化".to_string())
        })?;
        
        let screen = self.screen.as_ref().ok_or_else(|| {
            CaptureError::Backend("屏幕信息未初始化".to_string())
        })?;
        
        let root = self.root_window.ok_or_else(|| {
            CaptureError::Backend("根窗口未初始化".to_string())
        })?;
        
        // 获取屏幕尺寸
        let width = screen.width_in_pixels;
        let height = screen.height_in_pixels;
        
        // 创建一个异步任务来执行X11操作
        let conn_clone: Arc<XCBConnection> = Arc::clone(&conn);
        let (tx, rx) = oneshot::channel();
        
        tokio::task::spawn_blocking(move || {
            // 获取屏幕图像
            let image = match conn_clone.get_image(
                ImageFormat::Z_PIXMAP,
                root,
                0,
                0,
                width,
                height,
                !0, // 所有位平面
            ) {
                Ok(cookie) => match cookie.reply() {
                    Ok(reply) => reply,
                    Err(err) => {
                        let _ = tx.send(Err(CaptureError::Capture(format!("无法获取屏幕图像: {}", err))));
                        return;
                    }
                },
                Err(err) => {
                    let _ = tx.send(Err(CaptureError::Capture(format!("无法请求屏幕图像: {}", err))));
                    return;
                }
            };
            
            // 获取图像数据
            let data = image.data.to_vec();
            
            // 确定像素格式
            let format = match image.depth {
                24 => PixelFormat::RGB8,
                32 => PixelFormat::RGBA8,
                _ => {
                    let _ = tx.send(Err(CaptureError::Capture(format!("不支持的图像深度: {}", image.depth))));
                    return;
                }
            };
            
            // 计算行跨度
            let stride = width as u32 * (image.depth as u32 / 8);
            
            // 创建帧
            let frame = RawFrame::new(
                data,
                width as u32,
                height as u32,
                stride,
                format,
            );
            
            let _ = tx.send(Ok(frame));
        });
        
        // 等待帧或超时
        match timeout(Duration::from_secs(2), rx).await {
            Ok(Ok(frame)) => frame,
            Ok(Err(_)) => Err(CaptureError::Capture("帧接收通道已关闭".to_string())),
            Err(_) => Err(CaptureError::Timeout),
        }
    }
    
    /// 开始录制线程
    fn start_recording_thread(&mut self, fps: u32) -> Result<()> {
        // 设置录制状态
        if let Ok(mut is_recording) = self.is_recording.lock() {
            *is_recording = true;
        } else {
            return Err(CaptureError::Capture("无法设置录制状态".to_string()));
        }
        
        // 计算帧间隔
        let frame_interval = Duration::from_secs_f64(1.0 / fps as f64);
        
        // 创建录制线程
        let connection = self.connection.clone();
        let screen = self.screen.clone();
        let root_window = self.root_window;
        let recording_frames = self.recording_frames.clone();
        let is_recording = self.is_recording.clone();
        
        let handle = tokio::spawn(async move {
            let mut last_frame_time = SystemTime::now();
            
            loop {
                // 检查是否应该停止录制
                if let Ok(recording) = is_recording.lock() {
                    if !*recording {
                        break;
                    }
                } else {
                    break;
                }
                
                // 计算下一帧的时间
                let now = SystemTime::now();
                let elapsed = now.duration_since(last_frame_time).unwrap_or_default();
                
                if elapsed < frame_interval {
                    // 等待直到下一帧时间
                    let wait_time = frame_interval - elapsed;
                    tokio::time::sleep(wait_time).await;
                }
                
                // 更新帧时间
                last_frame_time = SystemTime::now();
                
                // 捕获帧
                if let (Some(conn), Some(screen), Some(root)) = (connection.as_ref(), screen.as_ref(), root_window) {
                    // 在阻塞任务中执行X11操作
                    let conn_clone: Arc<XCBConnection> = Arc::clone(&conn);
                    let screen_clone = screen.clone();
                    
                    if let Ok(frame) = tokio::task::spawn_blocking(move || {
                        // 获取屏幕图像
                        let image = match conn_clone.get_image(
                            ImageFormat::Z_PIXMAP,
                            root,
                            0,
                            0,
                            screen_clone.width_in_pixels,
                            screen_clone.height_in_pixels,
                            !0, // 所有位平面
                        ) {
                            Ok(cookie) => match cookie.reply() {
                                Ok(reply) => reply,
                                Err(_) => return None,
                            },
                            Err(_) => return None,
                        };
                        
                        // 获取图像数据
                        let data = image.data.to_vec();
                        
                        // 确定像素格式
                        let format = match image.depth {
                            24 => PixelFormat::RGB8,
                            32 => PixelFormat::RGBA8,
                            _ => return None,
                        };
                        
                        // 计算行跨度
                        let stride = screen_clone.width_in_pixels as u32 * (image.depth as u32 / 8);
                        
                        // 创建帧
                        Some(RawFrame::new(
                            data,
                            screen_clone.width_in_pixels as u32,
                            screen_clone.height_in_pixels as u32,
                            stride,
                            format,
                        ))
                    }).await {
                        if let Some(frame) = frame {
                            // 添加帧到录制缓冲
                            if let Ok(mut frames) = recording_frames.lock() {
                                frames.push(frame);
                            }
                        }
                    }
                }
            }
        });
        
        self.recording_handle = Some(handle);
        Ok(())
    }
    
    /// 停止录制线程
    async fn stop_recording_thread(&mut self) -> Result<()> {
        // 设置录制状态为false
        if let Ok(mut is_recording) = self.is_recording.lock() {
            *is_recording = false;
        }
        
        // 等待录制线程结束
        if let Some(handle) = self.recording_handle.take() {
            // 等待最多1秒
            match timeout(Duration::from_secs(1), handle).await {
                Ok(_) => {},
                Err(_) => {
                    // 超时，但无法中止已移动的句柄
                    // 记录警告信息
                    tracing::warn!("录制线程未能在超时时间内结束");
                }
            }
        }
        
        Ok(())
    }
}

#[async_trait]
impl CaptureBackend for X11CaptureBackend {
    async fn capture_frame(&mut self, _token: &PermissionToken) -> Result<RawFrame> {
        if !self.is_initialized {
            self.initialize()?;
        }
        
        // X11不需要权限令牌，直接捕获
        let frame = self.capture_screen().await?;
        
        // 保存最新帧
        if let Ok(mut latest) = self.latest_frame.lock() {
            *latest = Some(frame.clone());
        }
        
        Ok(frame)
    }
    
    async fn start_recording(&mut self, _token: &PermissionToken) -> Result<RecordingSession> {
        if !self.is_initialized {
            self.initialize()?;
        }
        
        // 获取屏幕信息
        let screen = self.screen.as_ref().ok_or_else(|| {
            CaptureError::Backend("屏幕信息未初始化".to_string())
        })?;
        
        // 清空录制帧缓冲
        if let Ok(mut frames) = self.recording_frames.lock() {
            frames.clear();
        }
        
        // 创建录制会话
        let session_id = format!("x11_recording_{}", uuid::Uuid::new_v4());
        let fps = 30; // 默认30fps
        let resolution = (
            screen.width_in_pixels as u32,
            screen.height_in_pixels as u32,
        );
        
        let session = RecordingSession::new(
            session_id,
            fps,
            resolution,
        );
        
        // 保存会话
        self.recording_session = Some(session.clone());
        
        // 启动录制线程
        self.start_recording_thread(fps)?;
        
        Ok(session)
    }
    
    async fn stop_recording(&mut self, session: RecordingSession) -> Result<Vec<u8>> {
        // 检查会话是否匹配
        if let Some(current_session) = &self.recording_session {
            if current_session.session_id != session.session_id {
                return Err(CaptureError::Capture("录制会话不匹配".to_string()));
            }
        } else {
            return Err(CaptureError::Capture("没有活动的录制会话".to_string()));
        }
        
        // 停止录制线程
        self.stop_recording_thread().await?;
        
        // 获取录制的帧
        let frames = if let Ok(frames) = self.recording_frames.lock() {
            frames.clone()
        } else {
            return Err(CaptureError::Capture("无法访问录制帧".to_string()));
        };
        
        if frames.is_empty() {
            return Err(CaptureError::Capture("没有捕获到帧".to_string()));
        }
        
        // 清理录制状态
        self.recording_session = None;
        if let Ok(mut frames_buffer) = self.recording_frames.lock() {
            frames_buffer.clear();
        }
        
        // 这里应该实现视频编码
        // 暂时返回第一帧的数据作为示例
        Ok(frames[0].data.clone())
    }
    
    fn is_available(&self) -> bool {
        // 检查X11是否可用
        self.display_name.is_some()
    }
    
    fn supported_formats(&self) -> Vec<PixelFormat> {
        vec![PixelFormat::RGBA8, PixelFormat::RGB8]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_x11_backend_availability() {
        // 这个测试只是检查创建是否会崩溃
        // 在没有X11环境的CI环境中可能会失败
        let backend = X11CaptureBackend::new();
        println!("X11后端创建结果: {:?}", backend);
    }
}
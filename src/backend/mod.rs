//! 捕获后端模块

pub mod common;
#[cfg(feature = "wayland")]
mod pipewire;
#[cfg(feature = "x11")]
pub mod x11;

pub use common::{CaptureBackend, PixelFormat, RawFrame, RecordingSession};
#[cfg(feature = "wayland")]
pub use pipewire::PipeWireCaptureBackend;
#[cfg(feature = "x11")]
pub use x11::X11CaptureBackend;

#[cfg(test)]
pub use common::MockCaptureBackend;

use crate::error::Result;
use std::env;

/// 检测当前显示服务器类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DisplayServerType {
    /// Wayland显示服务器
    Wayland,
    /// X11显示服务器
    X11,
    /// 未知或不支持的显示服务器
    Unknown,
}

/// 桌面环境类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DesktopEnvironment {
    /// GNOME桌面环境
    GNOME,
    /// KDE桌面环境
    KDE,
    /// 其他桌面环境
    Other,
    /// 未知桌面环境
    Unknown,
}

/// 显示服务器信息
#[derive(Debug, Clone)]
pub struct DisplayServerInfo {
    /// 显示服务器类型
    pub server_type: DisplayServerType,
    /// 桌面环境类型
    pub desktop_environment: DesktopEnvironment,
    /// 是否支持屏幕捕获
    pub supports_screen_capture: bool,
    /// 是否支持区域选择
    pub supports_region_selection: bool,
    /// 是否支持窗口选择
    pub supports_window_selection: bool,
    /// 是否支持多显示器
    pub supports_multi_monitor: bool,
}

impl DisplayServerInfo {
    /// 创建新的显示服务器信息
    pub fn new() -> Self {
        let server_type = detect_display_server();
        let desktop_environment = detect_desktop_environment();
        
        let mut info = Self {
            server_type,
            desktop_environment,
            supports_screen_capture: true, // 基本功能，默认支持
            supports_region_selection: false,
            supports_window_selection: false,
            supports_multi_monitor: false,
        };
        
        // 根据显示服务器类型和桌面环境设置功能支持
        match (server_type, desktop_environment) {
            (DisplayServerType::Wayland, DesktopEnvironment::GNOME) => {
                info.supports_region_selection = true;
                info.supports_window_selection = true;
                info.supports_multi_monitor = true;
            },
            (DisplayServerType::Wayland, DesktopEnvironment::KDE) => {
                info.supports_window_selection = true;
                info.supports_multi_monitor = true;
            },
            (DisplayServerType::X11, _) => {
                info.supports_region_selection = true;
                info.supports_window_selection = true;
                info.supports_multi_monitor = true;
            },
            _ => {}
        }
        
        info
    }
    
    /// 检查是否为GNOME环境
    pub fn is_gnome(&self) -> bool {
        self.desktop_environment == DesktopEnvironment::GNOME
    }
    
    /// 检查是否为Wayland环境
    pub fn is_wayland(&self) -> bool {
        self.server_type == DisplayServerType::Wayland
    }
    
    /// 检查是否为X11环境
    pub fn is_x11(&self) -> bool {
        self.server_type == DisplayServerType::X11
    }
}

impl Default for DisplayServerInfo {
    fn default() -> Self {
        Self::new()
    }
}

/// 获取当前显示服务器类型
pub fn detect_display_server() -> DisplayServerType {
    // 首先检查是否在Wayland会话中
    if let Ok(wayland_display) = env::var("WAYLAND_DISPLAY") {
        if !wayland_display.is_empty() {
            return DisplayServerType::Wayland;
        }
    }
    
    // 检查XDG_SESSION_TYPE环境变量
    if let Ok(session_type) = env::var("XDG_SESSION_TYPE") {
        if session_type.to_lowercase() == "wayland" {
            return DisplayServerType::Wayland;
        } else if session_type.to_lowercase() == "x11" {
            return DisplayServerType::X11;
        }
    }
    
    // 检查DISPLAY环境变量（X11）
    if let Ok(x_display) = env::var("DISPLAY") {
        if !x_display.is_empty() {
            return DisplayServerType::X11;
        }
    }
    
    // 无法确定显示服务器类型
    DisplayServerType::Unknown
}

/// 检测当前桌面环境
pub fn detect_desktop_environment() -> DesktopEnvironment {
    if let Ok(desktop) = env::var("XDG_CURRENT_DESKTOP") {
        let desktop = desktop.to_lowercase();
        
        if desktop.contains("gnome") {
            return DesktopEnvironment::GNOME;
        } else if desktop.contains("kde") {
            return DesktopEnvironment::KDE;
        } else {
            return DesktopEnvironment::Other;
        }
    }
    
    // 检查GNOME特有的环境变量
    if env::var("GNOME_DESKTOP_SESSION_ID").is_ok() {
        return DesktopEnvironment::GNOME;
    }
    
    // 检查KDE特有的环境变量
    if env::var("KDE_FULL_SESSION").is_ok() {
        return DesktopEnvironment::KDE;
    }
    
    DesktopEnvironment::Unknown
}

/// 创建适合当前环境的捕获后端
pub fn create_backend() -> Result<Box<dyn CaptureBackend>> {
    let info = DisplayServerInfo::new();
    
    match info.server_type {
        #[cfg(feature = "wayland")]
        DisplayServerType::Wayland => {
            let backend = PipeWireCaptureBackend::new();
            Ok(Box::new(backend))
        }
        #[cfg(not(feature = "wayland"))]
        DisplayServerType::Wayland => {
            Err(crate::error::CaptureError::Backend(
                "Wayland支持未启用，请使用 --features wayland 重新编译".to_string()
            ))
        }
        #[cfg(feature = "x11")]
        DisplayServerType::X11 => {
            let backend = X11CaptureBackend::new()?;
            Ok(Box::new(backend))
        }
        #[cfg(not(feature = "x11"))]
        DisplayServerType::X11 => {
            Err(crate::error::CaptureError::Backend(
                "X11支持未启用，请使用 --features x11 重新编译".to_string()
            ))
        }
        DisplayServerType::Unknown => {
            // 尝试自动检测可用的后端
            #[cfg(feature = "x11")]
            {
                if let Ok(backend) = X11CaptureBackend::new() {
                    if backend.is_available() {
                        return Ok(Box::new(backend));
                    }
                }
            }
            
            #[cfg(feature = "wayland")]
            {
                let backend = PipeWireCaptureBackend::new();
                if backend.is_available() {
                    return Ok(Box::new(backend));
                }
            }
            
            Err(crate::error::CaptureError::Backend(
                "未检测到支持的显示服务器".to_string()
            ))
        }
    }
}

/// 获取当前显示服务器信息
pub fn get_display_server_info() -> DisplayServerInfo {
    DisplayServerInfo::new()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_display_server_detection() {
        // 这个测试只是确保函数不会崩溃
        let server_type = detect_display_server();
        println!("检测到的显示服务器类型: {:?}", server_type);
    }
    
    #[test]
    fn test_desktop_environment_detection() {
        // 这个测试只是确保函数不会崩溃
        let desktop_env = detect_desktop_environment();
        println!("检测到的桌面环境: {:?}", desktop_env);
    }
    
    #[test]
    fn test_display_server_info() {
        let info = DisplayServerInfo::new();
        println!("显示服务器信息: {:?}", info);
        
        // 测试辅助方法
        let _ = info.is_gnome();
        let _ = info.is_wayland();
        let _ = info.is_x11();
    }
    
    #[test]
    fn test_create_backend() {
        // 这个测试只是确保函数不会崩溃
        let result = create_backend();
        println!("创建后端结果: {:?}", result);
    }
    
    #[test]
    fn test_get_display_server_info() {
        let info = get_display_server_info();
        println!("获取显示服务器信息: {:?}", info);
    }
    
    #[test]
    fn test_environment_variables() {
        // 测试环境变量检测逻辑
        // 保存原始环境变量
        let original_wayland = std::env::var("WAYLAND_DISPLAY").ok();
        let original_display = std::env::var("DISPLAY").ok();
        let original_session = std::env::var("XDG_SESSION_TYPE").ok();
        
        // 模拟Wayland环境
        std::env::set_var("WAYLAND_DISPLAY", "wayland-0");
        std::env::remove_var("DISPLAY");
        std::env::set_var("XDG_SESSION_TYPE", "wayland");
        
        let server_type = detect_display_server();
        assert_eq!(server_type, DisplayServerType::Wayland);
        
        // 模拟X11环境
        std::env::remove_var("WAYLAND_DISPLAY");
        std::env::set_var("DISPLAY", ":0");
        std::env::set_var("XDG_SESSION_TYPE", "x11");
        
        let server_type = detect_display_server();
        assert_eq!(server_type, DisplayServerType::X11);
        
        // 模拟未知环境
        std::env::remove_var("WAYLAND_DISPLAY");
        std::env::remove_var("DISPLAY");
        std::env::remove_var("XDG_SESSION_TYPE");
        
        let server_type = detect_display_server();
        assert_eq!(server_type, DisplayServerType::Unknown);
        
        // 恢复原始环境变量
        if let Some(val) = original_wayland {
            std::env::set_var("WAYLAND_DISPLAY", val);
        } else {
            std::env::remove_var("WAYLAND_DISPLAY");
        }
        
        if let Some(val) = original_display {
            std::env::set_var("DISPLAY", val);
        } else {
            std::env::remove_var("DISPLAY");
        }
        
        if let Some(val) = original_session {
            std::env::set_var("XDG_SESSION_TYPE", val);
        } else {
            std::env::remove_var("XDG_SESSION_TYPE");
        }
    }
}
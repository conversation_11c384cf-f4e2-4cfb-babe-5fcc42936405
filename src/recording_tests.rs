//! 录制操作控制测试

#[cfg(test)]
mod tests {
    use crate::controller::{OperationController, OperationState};
    use crate::config::{CaptureConfig, OperationType, OutputFormat};
    use crate::backend::common::{MockCaptureBackend, RawFrame, PixelFormat, RecordingSession};
    use crate::error::CaptureError;
    use std::path::PathBuf;
    use std::time::Duration;
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use tempfile::TempDir;

    // 创建测试配置
    fn create_test_config() -> (TempDir, CaptureConfig) {
        let temp_dir = TempDir::new().unwrap();
        let config = CaptureConfig {
            operation_type: OperationType::Recording,
            output_directory: temp_dir.path().to_path_buf(),
            output_format: OutputFormat::MP4,
            frame_rate: Some(30),
            duration: Some(Duration::from_secs(5)),
            quality: Some(90),
            ..Default::default()
        };
        
        (temp_dir, config)
    }
    
    #[tokio::test]
    async fn test_recording_execution() {
        // 创建测试配置和临时目录
        let (_temp_dir, config) = create_test_config();
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 执行录制操作
        let result = controller.execute(&config).await;
        
        // 验证结果
        assert!(result.is_ok(), "录制操作应该成功");
        
        let capture_result = result.unwrap();
        assert_eq!(capture_result.operation_type, OperationType::Recording);
        assert_eq!(capture_result.output_files.len(), 1);
        
        // 验证文件是否存在
        let output_file = &capture_result.output_files[0];
        assert!(output_file.exists(), "输出文件应该存在");
        
        // 验证文件扩展名
        assert_eq!(output_file.extension().unwrap(), "mp4");
        
        // 验证文件大小
        assert!(capture_result.total_size > 0);
        
        // 验证持续时间
        assert!(capture_result.duration.is_some());
        
        // 验证总帧数
        assert!(capture_result.total_frames.is_some());
    }
    
    #[tokio::test]
    async fn test_recording_with_different_formats() {
        // 测试不同的输出格式
        let formats = vec![
            OutputFormat::MP4,
            OutputFormat::WebM,
            OutputFormat::AVI,
            OutputFormat::MKV,
        ];
        
        for format in formats {
            // 创建测试配置和临时目录
            let (_temp_dir, mut config) = create_test_config();
            config.output_format = format;
            
            // 创建控制器
            let mut controller = OperationController::new(&config).unwrap();
            
            // 执行录制操作
            let result = controller.execute(&config).await;
            
            // 验证结果
            assert!(result.is_ok(), "使用格式 {:?} 的录制操作应该成功", format);
            
            let capture_result = result.unwrap();
            assert_eq!(capture_result.output_files.len(), 1);
            
            // 验证文件扩展名
            let output_file = &capture_result.output_files[0];
            assert_eq!(output_file.extension().unwrap(), format.extension());
        }
    }
    
    #[tokio::test]
    async fn test_recording_error_handling() {
        // 创建测试配置和临时目录
        let (_temp_dir, mut config) = create_test_config();
        
        // 设置不支持的格式
        config.output_format = OutputFormat::PNG;
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 执行录制操作
        let result = controller.execute(&config).await;
        
        // 验证结果
        assert!(result.is_err(), "使用不支持的格式应该失败");
        
        match result.unwrap_err() {
            CaptureError::Configuration(_) => {
                // 预期的错误类型
            },
            err => {
                panic!("预期配置错误，但得到: {:?}", err);
            }
        }
    }
    
    #[tokio::test]
    async fn test_recording_duration_control() {
        // 创建测试配置和临时目录，设置较短的录制时间
        let (_temp_dir, mut config) = create_test_config();
        config.duration = Some(Duration::from_millis(500)); // 0.5秒
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 执行录制操作
        let result = controller.execute(&config).await;
        
        // 验证结果
        assert!(result.is_ok(), "短时间录制应该成功");
        
        let capture_result = result.unwrap();
        
        // 验证录制时长接近预期值（允许一定误差）
        if let Some(duration) = capture_result.duration {
            // 由于异步执行和任务调度，实际时间可能会略长于设置时间
            assert!(duration >= Duration::from_millis(500), "录制时长应该至少为设置的时间");
            assert!(duration < Duration::from_secs(2), "录制时长不应该超过预期太多");
        } else {
            panic!("录制结果应该包含持续时间");
        }
    }
    
    #[tokio::test]
    async fn test_recording_manual_stop() {
        // 创建测试配置和临时目录，设置较长的录制时间
        let (_temp_dir, mut config) = create_test_config();
        config.duration = Some(Duration::from_secs(30)); // 30秒
        
        // 创建控制器
        let controller = Arc::new(Mutex::new(OperationController::new(&config).unwrap()));
        
        // 在另一个任务中执行录制
        let recording_task = {
            let controller_clone = controller.clone();
            let config_clone = config.clone();
            
            tokio::spawn(async move {
                let mut controller = controller_clone.lock().await;
                controller.execute(&config_clone).await
            })
        };
        
        // 等待一小段时间，确保录制已经开始
        tokio::time::sleep(Duration::from_millis(500)).await;
        
        // 手动停止录制
        {
            let mut controller = controller.lock().await;
            let stop_result = controller.stop().await;
            assert!(stop_result.is_ok(), "停止录制应该成功");
        }
        
        // 等待录制任务完成
        let result = recording_task.await.unwrap();
        
        // 验证结果
        assert!(result.is_ok(), "手动停止的录制应该正常完成");
        
        let capture_result = result.unwrap();
        
        // 验证录制时长小于设置的时间
        if let Some(duration) = capture_result.duration {
            assert!(duration < Duration::from_secs(30), "手动停止后，录制时长应该小于设置的时间");
        } else {
            panic!("录制结果应该包含持续时间");
        }
    }
    
    #[tokio::test]
    async fn test_recording_state_transitions() {
        // 创建测试配置和临时目录
        let (_temp_dir, config) = create_test_config();
        
        // 创建控制器
        let mut controller = OperationController::new(&config).unwrap();
        
        // 订阅状态变更事件
        let mut receiver = controller.subscribe_to_state_changes();
        
        // 在另一个任务中执行录制
        let handle = tokio::spawn({
            let config = config.clone();
            let controller = Arc::new(Mutex::new(controller));
            let controller_clone = controller.clone();
            
            async move {
                let mut controller = controller_clone.lock().await;
                controller.execute(&config).await
            }
        });
        
        // 收集状态变更
        let mut states = Vec::new();
        
        // 设置超时，防止测试无限等待
        let timeout = tokio::time::sleep(Duration::from_secs(10));
        
        tokio::select! {
            _ = timeout => {
                panic!("测试超时");
            }
            _ = async {
                while let Ok(event) = receiver.recv().await {
                    states.push(event.current_state);
                    if event.current_state == OperationState::Idle {
                        break;
                    }
                }
            } => {}
        }
        
        // 等待录制任务完成
        let _ = handle.await.unwrap();
        
        // 验证状态转换序列
        assert!(states.contains(&OperationState::Initializing), "应该包含初始化状态");
        assert!(states.contains(&OperationState::Recording), "应该包含录制状态");
        assert!(states.contains(&OperationState::Stopping), "应该包含停止状态");
        assert!(states.contains(&OperationState::Saving), "应该包含保存状态");
        assert!(states.contains(&OperationState::Idle), "应该包含空闲状态");
        
        // 验证状态转换顺序
        let recording_index = states.iter().position(|&s| s == OperationState::Recording).unwrap();
        let stopping_index = states.iter().position(|&s| s == OperationState::Stopping).unwrap();
        let saving_index = states.iter().position(|&s| s == OperationState::Saving).unwrap();
        
        assert!(recording_index < stopping_index, "录制状态应该在停止状态之前");
        assert!(stopping_index < saving_index, "停止状态应该在保存状态之前");
    }
}
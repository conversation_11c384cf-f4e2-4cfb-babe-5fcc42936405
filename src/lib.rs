//! # Wayland Screen Capture
//! 
//! 一个用于在Linux Wayland环境下进行屏幕截图和录制的Rust库。
//! 
//! ## 特性
//! 
//! - 支持屏幕截图和录制
//! - 通过xdg-desktop-portal处理权限申请
//! - 支持多种输出格式（PNG、JPEG、MP4、WebM等）
//! - 支持定时录制和间隔截图
//! - 异步API设计
//! 
//! ## 基本使用
//! 
//! ```rust,no_run
//! use wayland_screen_capture::{ScreenCapture, CaptureConfig, OperationType, OutputFormat};
//! use std::path::PathBuf;
//! 
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let config = CaptureConfig {
//!         operation_type: OperationType::Screenshot,
//!         output_directory: PathBuf::from("/tmp"),
//!         output_format: OutputFormat::PNG,
//!         ..Default::default()
//!     };
//!     
//!     let mut capture = ScreenCapture::new(config)?;
//!     let result = capture.execute().await?;
//!     
//!     println!("截图保存到: {:?}", result.output_files);
//!     Ok(())
//! }
//! ```

pub mod config;
pub mod error;
pub mod capture;
pub mod permission;
pub mod file_manager;
pub mod backend;
pub mod controller;
pub mod video_encoder;
pub mod logging;

// 测试模块
#[cfg(test)]
mod controller_tests;
#[cfg(test)]
mod recording_tests;

// 重新导出主要的公共API
pub use config::{CaptureConfig, OperationType, OutputFormat};
pub use error::{CaptureError, CaptureResult};
pub use capture::ScreenCapture;
pub use logging::{LogConfig, init_logging, init_default_logging, init_verbose_logging, init_debug_logging};

// 内部模块，不对外暴露实现细节
mod utils;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_basic_config_creation() {
        let config = CaptureConfig::default();
        assert_eq!(config.operation_type, OperationType::Screenshot);
        assert_eq!(config.output_format, OutputFormat::PNG);
    }
}
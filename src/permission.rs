//! 权限管理模块

use std::time::{Duration, SystemTime};
use std::collections::HashMap;
use zbus::{Connection, dbus_proxy, zvariant};
use uuid::Uuid;
use tokio::sync::oneshot;
use tokio::time::timeout;
use futures_util::stream::StreamExt;
use crate::error::{PermissionError, Result, CaptureError};
use crate::backend::detect_display_server;

// xdg-desktop-portal D-Bus接口定义

/// xdg-desktop-portal 屏幕捕获接口
#[dbus_proxy(
    interface = "org.freedesktop.portal.ScreenCast",
    default_service = "org.freedesktop.portal.Desktop",
    default_path = "/org/freedesktop/portal/desktop"
)]
trait ScreenCast {
    /// 创建一个新的屏幕捕获会话
    fn create_session(&self, options: HashMap<&str, zvariant::Value<'_>>) -> zbus::Result<(String,)>;
    
    /// 选择要捕获的源类型
    fn select_sources(&self, session_handle: &str, options: HashMap<&str, zvariant::Value<'_>>) -> zbus::Result<(String,)>;
    
    /// 开始捕获
    fn start(&self, session_handle: &str, parent_window: &str, options: HashMap<&str, zvariant::Value<'_>>) -> zbus::Result<(String,)>;
    
    /// 打开一个PipeWire远程连接
    fn open_pipe_wire_remote(&self, session_handle: &str, options: HashMap<&str, zvariant::Value<'_>>) -> zbus::Result<(u32,)>;
}

/// xdg-desktop-portal 会话接口
#[dbus_proxy(
    interface = "org.freedesktop.portal.Session",
    default_service = "org.freedesktop.portal.Desktop",
    default_path = "/org/freedesktop/portal/desktop"
)]
trait Session {
    /// 关闭会话
    fn close(&self) -> zbus::Result<()>;
}

/// xdg-desktop-portal 请求接口
#[dbus_proxy(
    interface = "org.freedesktop.portal.Request",
    default_service = "org.freedesktop.portal.Desktop",
    default_path = "/org/freedesktop/portal/desktop/request"
)]
trait Request {
    /// 取消请求
    fn cancel(&self) -> zbus::Result<()>;
    
    /// 请求响应信号
    #[dbus_proxy(signal)]
    fn response(&self, response: u32, results: HashMap<String, zvariant::Value<'_>>) -> zbus::Result<()>;
}

/// 权限令牌
#[derive(Debug, Clone)]
pub struct PermissionToken {
    /// 会话句柄
    pub session_handle: String,
    /// 节点ID
    pub node_id: Option<u32>,
    /// 过期时间
    pub expires_at: SystemTime,
    /// 是否为永久令牌
    pub is_permanent: bool,
    /// 捕获源类型
    pub source_type: Option<SourceType>,
    /// 是否为GNOME环境
    pub is_gnome: bool,
}

impl Default for PermissionToken {
    fn default() -> Self {
        Self::permanent()
    }
}

impl PermissionToken {
    /// 检查令牌是否有效
    pub fn is_valid(&self) -> bool {
        self.is_permanent || SystemTime::now() < self.expires_at
    }
    
    /// 获取剩余有效时间
    pub fn remaining_time(&self) -> Option<Duration> {
        if self.is_permanent {
            None // 永久有效，没有剩余时间的概念
        } else {
            match self.expires_at.duration_since(SystemTime::now()) {
                Ok(duration) => Some(duration),
                Err(_) => Some(Duration::from_secs(0)), // 已过期
            }
        }
    }
    
    /// 创建一个永久有效的令牌（用于X11等不需要特殊权限的环境）
    pub fn permanent() -> Self {
        Self {
            session_handle: "permanent".to_string(),
            node_id: None,
            // 设置为100年后过期
            expires_at: SystemTime::now() + Duration::from_secs(60 * 60 * 24 * 365 * 100),
            is_permanent: true,
            source_type: None,
            is_gnome: false,
        }
    }
    
    /// 创建一个临时令牌
    pub fn temporary(session_handle: String, node_id: Option<u32>, duration: Duration, source_type: SourceType, is_gnome: bool) -> Self {
        Self {
            session_handle,
            node_id,
            expires_at: SystemTime::now() + duration,
            is_permanent: false,
            source_type: Some(source_type),
            is_gnome,
        }
    }
    
    /// 检查令牌是否即将过期（小于5分钟）
    pub fn is_expiring_soon(&self) -> bool {
        if self.is_permanent {
            false
        } else {
            match self.expires_at.duration_since(SystemTime::now()) {
                Ok(duration) => duration < Duration::from_secs(5 * 60),
                Err(_) => true, // 已过期
            }
        }
    }
    
    /// 刷新令牌有效期
    pub fn refresh(&mut self, duration: Duration) {
        if !self.is_permanent {
            self.expires_at = SystemTime::now() + duration;
        }
    }
}

/// 权限状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PermissionStatus {
    /// 未申请
    NotRequested,
    /// 申请中
    Pending,
    /// 已授权
    Granted,
    /// 被拒绝
    Denied,
    /// 已过期
    Expired,
}

/// 权限管理器trait
#[async_trait::async_trait]
pub trait PermissionManager: Send + Sync + std::fmt::Debug {
    /// 申请屏幕捕获权限
    async fn request_screen_capture_permission(&mut self) -> Result<PermissionToken>;
    
    /// 检查权限状态
    async fn check_permission_status(&self) -> Result<PermissionStatus>;
    
    /// 清理会话
    fn cleanup_session(&mut self) -> Result<()>;
    
    /// 刷新权限令牌
    async fn refresh_permission(&mut self) -> Result<PermissionToken>;
    
    /// 检查权限是否即将过期
    async fn is_permission_expiring_soon(&self) -> Result<bool>;
    
    /// 设置捕获源类型
    fn set_source_type(&mut self, source_type: SourceType);
    
    /// 获取当前捕获源类型
    fn get_source_type(&self) -> SourceType;
}

/// 屏幕捕获源类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SourceType {
    /// 整个屏幕
    Monitor,
    /// 单个窗口
    Window,
    /// 区域选择
    Region,
}

/// 屏幕捕获请求结果
#[derive(Debug, Clone)]
struct PortalResponse {
    /// 响应代码（0表示成功）
    response: u32,
    /// 结果数据
    results: HashMap<String, zvariant::OwnedValue>,
}

/// xdg-desktop-portal权限管理器（用于Wayland）
#[derive(Debug)]
pub struct XdgPortalPermissionManager {
    /// 会话句柄
    session_handle: Option<String>,
    /// 权限令牌
    permission_token: Option<PermissionToken>,
    /// D-Bus连接
    connection: Option<Connection>,
    /// 捕获源类型
    source_type: SourceType,
    /// 是否为GNOME环境
    is_gnome: bool,
}

impl XdgPortalPermissionManager {
    /// 创建新的权限管理器
    pub fn new() -> Self {
        Self {
            session_handle: None,
            permission_token: None,
            connection: None,
            source_type: SourceType::Monitor, // 默认捕获整个屏幕
            is_gnome: Self::detect_gnome_environment(),
        }
    }
    
    /// 设置捕获源类型
    pub fn with_source_type(mut self, source_type: SourceType) -> Self {
        self.source_type = source_type;
        self
    }
    
    /// 检测是否为GNOME环境
    fn detect_gnome_environment() -> bool {
        if let Ok(desktop) = std::env::var("XDG_CURRENT_DESKTOP") {
            return desktop.to_lowercase().contains("gnome");
        }
        false
    }
    
    /// 创建D-Bus连接
    async fn create_connection(&mut self) -> Result<&Connection> {
        if self.connection.is_none() {
            match Connection::session().await {
                Ok(conn) => {
                    self.connection = Some(conn);
                },
                Err(err) => {
                    return Err(PermissionError::DBusError(err.to_string()).into());
                }
            }
        }
        
        Ok(self.connection.as_ref().unwrap())
    }
    
    /// 创建屏幕捕获会话
    async fn create_session(&mut self) -> Result<String> {
        let conn = self.create_connection().await?;
        
        // 创建ScreenCast代理
        let proxy = match ScreenCastProxy::new(conn).await {
            Ok(proxy) => proxy,
            Err(err) => {
                return Err(PermissionError::DBusError(format!("无法创建ScreenCast代理: {}", err)).into());
            }
        };
        
        // 准备会话选项
        let mut options = HashMap::new();
        options.insert("handle_token", zvariant::Value::new(Uuid::new_v4().to_string()));
        options.insert("session_handle_token", zvariant::Value::new(Uuid::new_v4().to_string()));
        
        // 创建会话
        let (request_path, ) = match proxy.create_session(options).await {
            Ok((path, )) => (path, ),
            Err(err) => {
                return Err(PermissionError::DBusError(format!("创建会话请求失败: {}", err)).into());
            }
        };
        
        // 等待会话创建响应
        let response = self.wait_for_response(request_path).await?;
        
        if response.response != 0 {
            return Err(PermissionError::DBusError(format!("会话创建被拒绝，代码: {}", response.response)).into());
        }
        
        // 从响应中获取会话句柄
        let session_handle = match response.results.get("session_handle") {
            Some(value) => {
                if let Some(handle) = value.downcast_ref::<str>() {
                    handle.to_string()
                } else {
                    return Err(PermissionError::DBusError("无法解析会话句柄".to_string()).into());
                }
            },
            None => {
                return Err(PermissionError::DBusError("响应中没有会话句柄".to_string()).into());
            }
        };
        
        self.session_handle = Some(session_handle.clone());
        Ok(session_handle)
    }
    
    /// 选择捕获源
    async fn select_sources(&self, session_handle: &str) -> Result<String> {
        let conn = self.connection.as_ref().unwrap();
        
        // 创建ScreenCast代理
        let proxy = match ScreenCastProxy::new(conn).await {
            Ok(proxy) => proxy,
            Err(err) => {
                return Err(PermissionError::DBusError(format!("无法创建ScreenCast代理: {}", err)).into());
            }
        };
        
        // 准备源选择选项
        let mut options = HashMap::new();
        options.insert("handle_token", zvariant::Value::new(Uuid::new_v4().to_string()));
        
        // 设置捕获类型
        let mut types = 0u32;
        match self.source_type {
            SourceType::Monitor => types |= 1, // 1 = MONITOR
            SourceType::Window => types |= 2,  // 2 = WINDOW
            SourceType::Region => {
                // GNOME支持区域选择，其他环境可能不支持
                if self.is_gnome {
                    types |= 4; // 4 = REGION (GNOME特有)
                } else {
                    // 如果不是GNOME，回退到整个屏幕
                    types |= 1;
                }
            }
        }
        options.insert("types", zvariant::Value::new(types));
        
        // 设置多显示器支持
        options.insert("multiple", zvariant::Value::new(false)); // 暂时只支持单显示器
        
        // 选择源
        let (request_path, ) = match proxy.select_sources(session_handle, options).await {
            Ok((path, )) => (path, ),
            Err(err) => {
                return Err(PermissionError::DBusError(format!("选择源请求失败: {}", err)).into());
            }
        };
        
        Ok(request_path)
    }
    
    /// 开始捕获
    async fn start_capture(&self, session_handle: &str) -> Result<String> {
        let conn = self.connection.as_ref().unwrap();
        
        // 创建ScreenCast代理
        let proxy = match ScreenCastProxy::new(conn).await {
            Ok(proxy) => proxy,
            Err(err) => {
                return Err(PermissionError::DBusError(format!("无法创建ScreenCast代理: {}", err)).into());
            }
        };
        
        // 准备开始捕获选项
        let mut options = HashMap::new();
        options.insert("handle_token", zvariant::Value::new(Uuid::new_v4().to_string()));
        
        // 设置光标模式（1 = 隐藏，2 = 嵌入，3 = 元数据）
        options.insert("cursor_mode", zvariant::Value::new(2u32));
        
        // 开始捕获
        let (request_path, ) = match proxy.start(session_handle, "", options).await {
            Ok((path, )) => (path, ),
            Err(err) => {
                return Err(PermissionError::DBusError(format!("开始捕获请求失败: {}", err)).into());
            }
        };
        
        Ok(request_path)
    }
    
    /// 打开PipeWire远程连接
    async fn open_pipewire_remote(&self, session_handle: &str) -> Result<u32> {
        let conn = self.connection.as_ref().unwrap();
        
        // 创建ScreenCast代理
        let proxy = match ScreenCastProxy::new(conn).await {
            Ok(proxy) => proxy,
            Err(err) => {
                return Err(PermissionError::DBusError(format!("无法创建ScreenCast代理: {}", err)).into());
            }
        };
        
        // 准备选项
        let options = HashMap::new();
        
        // 打开PipeWire远程连接
        let (node_id, ) = match proxy.open_pipe_wire_remote(session_handle, options).await {
            Ok((id, )) => (id, ),
            Err(err) => {
                return Err(PermissionError::DBusError(format!("打开PipeWire远程连接请求失败: {}", err)).into());
            }
        };
        
        Ok(node_id)
    }
    
    /// 等待D-Bus响应
    async fn wait_for_response(&self, request_path: String) -> Result<PortalResponse> {
        let conn = self.connection.as_ref().unwrap();
        
        // 创建Request代理
        let proxy = RequestProxy::builder(conn)
            .path(request_path)
            .map_err(|e| PermissionError::DBusError(e.to_string()))?
            .build()
            .await
            .map_err(|e| PermissionError::DBusError(e.to_string()))?;
        
        // 创建通道接收响应
        let (tx, rx) = oneshot::channel();
        
        // 监听响应信号
        let mut response_stream = proxy.receive_response()
            .await
            .map_err(|e| PermissionError::DBusError(e.to_string()))?;
        
        // 在单独的任务中处理响应
        tokio::spawn(async move {
            if let Some(response) = response_stream.next().await {
                if let Ok(args) = response.args() {
                    let response_code = args.response;
                    let results = args.results;
                    
                    let owned_results = results.iter()
                        .map(|(k, v)| (k.clone(), v.to_owned()))
                        .collect();
                    
                    let _ = tx.send(PortalResponse {
                        response: response_code,
                        results: owned_results,
                    });
                }
            }
        });
        
        // 等待响应，设置超时
        match timeout(Duration::from_secs(30), rx).await {
            Ok(Ok(response)) => Ok(response),
            Ok(Err(_)) => Err(PermissionError::DBusError("响应通道已关闭".to_string()).into()),
            Err(_) => Err(PermissionError::Timeout.into()),
        }
    }
    
    /// 关闭会话
    async fn close_session(&mut self) -> Result<()> {
        if let Some(session_handle) = &self.session_handle {
            if let Some(conn) = &self.connection {
                // 构建会话路径
                let session_path = format!("/org/freedesktop/portal/desktop/session/{}", session_handle);
                
                // 创建Session代理
                match SessionProxy::builder(conn)
                    .path(session_path)
                    .map_err(|e| PermissionError::DBusError(e.to_string()))?
                    .build()
                    .await
                    .map_err(|e| PermissionError::DBusError(e.to_string())) {
                    Ok(proxy) => {
                        let _ = proxy.close().await;
                    },
                    Err(_) => {
                        // 忽略错误，会话可能已经关闭
                    }
                }
            }
            
            self.session_handle = None;
            self.permission_token = None;
        }
        
        Ok(())
    }
}

impl Default for XdgPortalPermissionManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait::async_trait]
impl PermissionManager for XdgPortalPermissionManager {
    async fn request_screen_capture_permission(&mut self) -> Result<PermissionToken> {
        // 检查当前显示服务器类型
        match detect_display_server() {
            crate::backend::DisplayServerType::Wayland => {
                // 清理旧会话
                if self.session_handle.is_some() {
                    let _ = self.close_session().await;
                }
                
                // 创建新会话
                let session_handle = self.create_session().await?;
                
                // 选择捕获源
                let request_path = self.select_sources(&session_handle).await?;
                
                // 等待源选择响应
                let response = self.wait_for_response(request_path).await?;
                
                if response.response != 0 {
                    return Err(PermissionError::Denied.into());
                }
                
                // 开始捕获
                let request_path = self.start_capture(&session_handle).await?;
                
                // 等待捕获开始响应
                let response = self.wait_for_response(request_path).await?;
                
                if response.response != 0 {
                    return Err(PermissionError::Denied.into());
                }
                
                // 打开PipeWire远程连接
                let node_id = self.open_pipewire_remote(&session_handle).await?;
                
                // 创建权限令牌
                let token = PermissionToken::temporary(
                    session_handle.clone(),
                    Some(node_id),
                    Duration::from_secs(3600), // 1小时有效期
                    self.source_type,
                    self.is_gnome
                );
                
                self.permission_token = Some(token.clone());
                Ok(token)
            },
            crate::backend::DisplayServerType::X11 => {
                // X11不需要特殊权限
                let token = PermissionToken::permanent();
                self.permission_token = Some(token.clone());
                Ok(token)
            },
            crate::backend::DisplayServerType::Unknown => {
                Err(PermissionError::PortalUnavailable.into())
            }
        }
    }
    
    async fn check_permission_status(&self) -> Result<PermissionStatus> {
        if let Some(token) = &self.permission_token {
            if token.is_valid() {
                Ok(PermissionStatus::Granted)
            } else {
                Ok(PermissionStatus::Expired)
            }
        } else {
            Ok(PermissionStatus::NotRequested)
        }
    }
    
    fn cleanup_session(&mut self) -> Result<()> {
        // 在同步上下文中，我们不能直接调用异步方法
        // 但我们可以清理本地状态
        self.session_handle = None;
        self.permission_token = None;
        self.connection = None;
        Ok(())
    }
    
    async fn refresh_permission(&mut self) -> Result<PermissionToken> {
        // 检查当前权限状态
        let status = self.check_permission_status().await?;
        
        match status {
            PermissionStatus::Granted => {
                // 如果是永久令牌，直接返回
                if let Some(token) = &self.permission_token {
                    if token.is_permanent {
                        return Ok(token.clone());
                    }
                    
                    // 如果令牌即将过期，重新申请权限
                    if token.is_expiring_soon() {
                        return self.request_screen_capture_permission().await;
                    } else {
                        // 刷新令牌有效期
                        let mut refreshed_token = token.clone();
                        refreshed_token.refresh(Duration::from_secs(3600)); // 延长1小时
                        self.permission_token = Some(refreshed_token.clone());
                        return Ok(refreshed_token);
                    }
                }
            },
            _ => {
                // 如果没有有效的权限，重新申请
                return self.request_screen_capture_permission().await;
            }
        }
        
        // 如果没有权限令牌，重新申请
        self.request_screen_capture_permission().await
    }
    
    async fn is_permission_expiring_soon(&self) -> Result<bool> {
        if let Some(token) = &self.permission_token {
            Ok(token.is_expiring_soon())
        } else {
            // 如果没有令牌，视为即将过期
            Ok(true)
        }
    }
    
    fn set_source_type(&mut self, source_type: SourceType) {
        self.source_type = source_type;
    }
    
    fn get_source_type(&self) -> SourceType {
        self.source_type
    }
}

/// 创建适合当前环境的权限管理器
pub fn create_permission_manager() -> Box<dyn PermissionManager> {
    Box::new(XdgPortalPermissionManager::new())
}

// 为 zbus::Error 实现 From trait，以便在 ? 操作符中使用
impl From<zbus::Error> for CaptureError {
    fn from(err: zbus::Error) -> Self {
        CaptureError::Permission(PermissionError::DBusError(err.to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_token_validity() {
        let token = PermissionToken {
            session_handle: "test_session".to_string(),
            node_id: Some(123),
            expires_at: SystemTime::now() + Duration::from_secs(3600),
            is_permanent: false,
            source_type: Some(SourceType::Monitor),
            is_gnome: false,
        };
        
        assert!(token.is_valid());
    }

    #[test]
    fn test_permanent_token() {
        let token = PermissionToken::permanent();
        assert!(token.is_valid());
        assert!(token.node_id.is_none());
    }

    #[test]
    fn test_permission_manager_creation() {
        let manager = XdgPortalPermissionManager::new();
        assert!(manager.session_handle.is_none());
        assert!(manager.permission_token.is_none());
    }
}
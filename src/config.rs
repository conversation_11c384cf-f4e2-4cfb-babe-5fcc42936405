//! 配置管理模块

use std::path::PathBuf;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use crate::error::{ConfigError, Result};

/// 操作类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OperationType {
    /// 单次截图
    Screenshot,
    /// 视频录制
    Recording,
    /// 间隔截图
    IntervalScreenshot,
}

impl Default for OperationType {
    fn default() -> Self {
        Self::Screenshot
    }
}

/// 输出格式
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OutputFormat {
    // 图片格式
    PNG,
    JPEG,
    BMP,
    WEBP,
    
    // 视频格式
    MP4,
    WebM,
    AVI,
    MKV,
}

impl Default for OutputFormat {
    fn default() -> Self {
        Self::PNG
    }
}

impl OutputFormat {
    /// 检查格式是否为图片格式
    pub fn is_image(&self) -> bool {
        matches!(self, Self::PNG | Self::JPEG | Self::BMP | Self::WEBP)
    }
    
    /// 检查格式是否为视频格式
    pub fn is_video(&self) -> bool {
        matches!(self, Self::MP4 | Self::WebM | Self::AVI | Self::MKV)
    }
    
    /// 获取文件扩展名
    pub fn extension(&self) -> &'static str {
        match self {
            Self::PNG => "png",
            Self::JPEG => "jpg",
            Self::BMP => "bmp",
            Self::WEBP => "webp",
            Self::MP4 => "mp4",
            Self::WebM => "webm",
            Self::AVI => "avi",
            Self::MKV => "mkv",
        }
    }
}

/// 捕获配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CaptureConfig {
    /// 操作类型
    pub operation_type: OperationType,
    
    /// 输出目录
    pub output_directory: PathBuf,
    
    /// 输出格式
    pub output_format: OutputFormat,
    
    /// 录制时长（仅录制时有效）
    pub duration: Option<Duration>,
    
    /// 间隔时间（仅间隔截图时有效）
    pub interval: Option<Duration>,
    
    /// 总监控时长（仅间隔截图时有效）
    pub total_duration: Option<Duration>,
    
    /// 质量设置（1-100，仅适用于有损格式）
    pub quality: Option<u8>,
    
    /// 帧率（仅录制时有效）
    pub frame_rate: Option<u32>,
    
    /// 自定义文件名前缀
    pub filename_prefix: Option<String>,
    
    /// 是否覆盖已存在的文件
    pub overwrite_existing: bool,
    
    /// 是否启用详细日志
    pub verbose_logging: bool,

    /// 日志文件路径（可选）
    pub log_file_path: Option<PathBuf>,
}

impl Default for CaptureConfig {
    fn default() -> Self {
        Self {
            operation_type: OperationType::Screenshot,
            output_directory: std::env::temp_dir(),
            output_format: OutputFormat::PNG,
            duration: None,
            interval: None,
            total_duration: None,
            quality: None,
            frame_rate: Some(30),
            filename_prefix: None,
            overwrite_existing: false,
            verbose_logging: false,
            log_file_path: None,
        }
    }
}

impl CaptureConfig {
    /// 创建新的配置实例
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 设置操作类型
    pub fn with_operation_type(mut self, operation_type: OperationType) -> Self {
        self.operation_type = operation_type;
        self
    }
    
    /// 设置输出目录
    pub fn with_output_directory<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.output_directory = path.into();
        self
    }
    
    /// 设置输出格式
    pub fn with_output_format(mut self, format: OutputFormat) -> Self {
        self.output_format = format;
        self
    }
    
    /// 设置录制时长
    pub fn with_duration(mut self, duration: Duration) -> Self {
        self.duration = Some(duration);
        self
    }
    
    /// 设置间隔时间
    pub fn with_interval(mut self, interval: Duration) -> Self {
        self.interval = Some(interval);
        self
    }
    
    /// 设置质量
    pub fn with_quality(mut self, quality: u8) -> Self {
        self.quality = Some(quality);
        self
    }

    /// 启用详细日志
    pub fn with_verbose_logging(mut self, verbose: bool) -> Self {
        self.verbose_logging = verbose;
        self
    }

    /// 设置日志文件
    pub fn with_log_file<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.log_file_path = Some(path.into());
        self
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        // 检查输出目录
        if !self.output_directory.exists() {
            return Err(ConfigError::InvalidOutputDirectory {
                path: self.output_directory.clone(),
            }.into());
        }
        
        // 检查格式与操作类型的兼容性
        match self.operation_type {
            OperationType::Screenshot | OperationType::IntervalScreenshot => {
                if !self.output_format.is_image() {
                    return Err(ConfigError::UnsupportedFormat {
                        format: format!("{:?} for screenshot", self.output_format),
                    }.into());
                }
            }
            OperationType::Recording => {
                if !self.output_format.is_video() {
                    return Err(ConfigError::UnsupportedFormat {
                        format: format!("{:?} for recording", self.output_format),
                    }.into());
                }
            }
        }
        
        // 检查时长设置
        if let Some(duration) = self.duration {
            if duration.is_zero() {
                return Err(ConfigError::InvalidDuration { duration }.into());
            }
            
            // 录制时长不应超过24小时
            if duration > Duration::from_secs(24 * 3600) {
                return Err(ConfigError::InvalidDuration { duration }.into());
            }
        }
        
        // 检查间隔设置
        if let Some(interval) = self.interval {
            if interval.is_zero() {
                return Err(ConfigError::InvalidInterval { interval }.into());
            }
            
            // 间隔不应小于100毫秒
            if interval < Duration::from_millis(100) {
                return Err(ConfigError::InvalidInterval { interval }.into());
            }
        }
        
        // 检查质量设置
        if let Some(quality) = self.quality {
            if quality == 0 || quality > 100 {
                return Err(ConfigError::InvalidQuality { quality }.into());
            }
        }
        
        // 检查帧率设置
        if let Some(frame_rate) = self.frame_rate {
            if frame_rate == 0 || frame_rate > 120 {
                return Err(ConfigError::ConflictingParameters {
                    message: format!("Invalid frame rate: {}", frame_rate),
                }.into());
            }
        }
        
        // 检查参数冲突
        match self.operation_type {
            OperationType::Screenshot => {
                if self.duration.is_some() {
                    return Err(ConfigError::ConflictingParameters {
                        message: "Duration cannot be set for screenshot".to_string(),
                    }.into());
                }
                if self.interval.is_some() {
                    return Err(ConfigError::ConflictingParameters {
                        message: "Interval cannot be set for single screenshot".to_string(),
                    }.into());
                }
            }
            OperationType::Recording => {
                if self.interval.is_some() {
                    return Err(ConfigError::ConflictingParameters {
                        message: "Interval cannot be set for recording".to_string(),
                    }.into());
                }
            }
            OperationType::IntervalScreenshot => {
                if self.interval.is_none() {
                    return Err(ConfigError::ConflictingParameters {
                        message: "Interval must be set for interval screenshot".to_string(),
                    }.into());
                }
            }
        }
        
        Ok(())
    }
    
    /// 应用默认值
    pub fn apply_defaults(&mut self) {
        // 设置默认质量
        if self.quality.is_none() {
            match self.output_format {
                OutputFormat::JPEG => self.quality = Some(85),
                OutputFormat::WEBP => self.quality = Some(80),
                _ => {}
            }
        }
        
        // 设置默认帧率
        if self.frame_rate.is_none() && self.operation_type == OperationType::Recording {
            self.frame_rate = Some(30);
        }
        
        // 设置默认间隔
        if self.interval.is_none() && self.operation_type == OperationType::IntervalScreenshot {
            self.interval = Some(Duration::from_secs(1));
        }
        
        // 设置默认录制时长（如果未指定）
        if self.duration.is_none() && self.operation_type == OperationType::Recording {
            // 默认录制10分钟
            self.duration = Some(Duration::from_secs(10 * 60));
        }
        
        // 设置默认总监控时长（如果未指定）
        if self.total_duration.is_none() && self.operation_type == OperationType::IntervalScreenshot {
            // 默认监控1小时
            self.total_duration = Some(Duration::from_secs(60 * 60));
        }
        
        // 设置默认文件名前缀
        if self.filename_prefix.is_none() {
            let prefix = match self.operation_type {
                OperationType::Screenshot => "screenshot",
                OperationType::Recording => "recording",
                OperationType::IntervalScreenshot => "monitor",
            };
            self.filename_prefix = Some(prefix.to_string());
        }
    }
    
    /// 获取有效的输出格式
    pub fn get_effective_format(&self) -> OutputFormat {
        match self.operation_type {
            OperationType::Screenshot | OperationType::IntervalScreenshot => {
                if self.output_format.is_image() {
                    self.output_format
                } else {
                    // 如果指定了视频格式但操作是截图，默认使用PNG
                    OutputFormat::PNG
                }
            },
            OperationType::Recording => {
                if self.output_format.is_video() {
                    self.output_format
                } else {
                    // 如果指定了图片格式但操作是录制，默认使用MP4
                    OutputFormat::MP4
                }
            }
        }
    }
    
    /// 获取有效的质量设置
    pub fn get_effective_quality(&self) -> u8 {
        match self.quality {
            Some(q) => q.clamp(1, 100),
            None => match self.output_format {
                OutputFormat::JPEG => 85,
                OutputFormat::WEBP => 80,
                _ => 90, // 其他格式的默认质量
            }
        }
    }
    
    /// 获取有效的帧率
    pub fn get_effective_frame_rate(&self) -> u32 {
        match self.frame_rate {
            Some(rate) => rate.clamp(1, 120),
            None => 30, // 默认30fps
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_default_config() {
        let config = CaptureConfig::default();
        assert_eq!(config.operation_type, OperationType::Screenshot);
        assert_eq!(config.output_format, OutputFormat::PNG);
        assert!(!config.overwrite_existing);
    }

    #[test]
    fn test_config_builder() {
        let config = CaptureConfig::new()
            .with_operation_type(OperationType::Recording)
            .with_output_format(OutputFormat::MP4)
            .with_duration(Duration::from_secs(60))
            .with_quality(90);
        
        assert_eq!(config.operation_type, OperationType::Recording);
        assert_eq!(config.output_format, OutputFormat::MP4);
        assert_eq!(config.duration, Some(Duration::from_secs(60)));
        assert_eq!(config.quality, Some(90));
    }

    #[test]
    fn test_format_validation() {
        assert!(OutputFormat::PNG.is_image());
        assert!(!OutputFormat::PNG.is_video());
        assert!(OutputFormat::MP4.is_video());
        assert!(!OutputFormat::MP4.is_image());
    }

    #[test]
    fn test_config_validation() {
        let mut config = CaptureConfig::default();
        config.output_directory = std::env::temp_dir();
        
        // 有效配置应该通过验证
        assert!(config.validate().is_ok());
        
        // 无效质量应该失败
        config.quality = Some(150);
        assert!(config.validate().is_err());
    }
    
    #[test]
    fn test_apply_defaults() {
        // 测试截图默认值
        let mut screenshot_config = CaptureConfig::new()
            .with_operation_type(OperationType::Screenshot);
        screenshot_config.apply_defaults();
        
        assert_eq!(screenshot_config.filename_prefix, Some("screenshot".to_string()));
        assert!(screenshot_config.duration.is_none()); // 截图不需要时长
        
        // 测试录制默认值
        let mut recording_config = CaptureConfig::new()
            .with_operation_type(OperationType::Recording);
        recording_config.apply_defaults();
        
        assert_eq!(recording_config.filename_prefix, Some("recording".to_string()));
        assert_eq!(recording_config.duration, Some(Duration::from_secs(10 * 60))); // 默认10分钟
        assert_eq!(recording_config.frame_rate, Some(30)); // 默认30fps
        
        // 测试间隔截图默认值
        let mut interval_config = CaptureConfig::new()
            .with_operation_type(OperationType::IntervalScreenshot);
        interval_config.apply_defaults();
        
        assert_eq!(interval_config.filename_prefix, Some("monitor".to_string()));
        assert_eq!(interval_config.interval, Some(Duration::from_secs(1))); // 默认1秒间隔
        assert_eq!(interval_config.total_duration, Some(Duration::from_secs(60 * 60))); // 默认1小时
    }
    
    #[test]
    fn test_get_effective_format() {
        // 测试截图操作
        let mut screenshot_config = CaptureConfig::new()
            .with_operation_type(OperationType::Screenshot);
        
        // 正确的图片格式
        screenshot_config.output_format = OutputFormat::PNG;
        assert_eq!(screenshot_config.get_effective_format(), OutputFormat::PNG);
        
        // 错误的视频格式，应该返回默认PNG
        screenshot_config.output_format = OutputFormat::MP4;
        assert_eq!(screenshot_config.get_effective_format(), OutputFormat::PNG);
        
        // 测试录制操作
        let mut recording_config = CaptureConfig::new()
            .with_operation_type(OperationType::Recording);
        
        // 正确的视频格式
        recording_config.output_format = OutputFormat::MP4;
        assert_eq!(recording_config.get_effective_format(), OutputFormat::MP4);
        
        // 错误的图片格式，应该返回默认MP4
        recording_config.output_format = OutputFormat::PNG;
        assert_eq!(recording_config.get_effective_format(), OutputFormat::MP4);
    }
    
    #[test]
    fn test_get_effective_quality() {
        let mut config = CaptureConfig::default();
        
        // 测试指定质量
        config.quality = Some(75);
        assert_eq!(config.get_effective_quality(), 75);
        
        // 测试超出范围的质量
        config.quality = Some(150);
        assert_eq!(config.get_effective_quality(), 100); // 应该被限制在100
        
        config.quality = Some(0);
        assert_eq!(config.get_effective_quality(), 1); // 应该被限制在1
        
        // 测试默认质量
        config.quality = None;
        config.output_format = OutputFormat::JPEG;
        assert_eq!(config.get_effective_quality(), 85); // JPEG默认质量
        
        config.output_format = OutputFormat::WEBP;
        assert_eq!(config.get_effective_quality(), 80); // WEBP默认质量
        
        config.output_format = OutputFormat::PNG;
        assert_eq!(config.get_effective_quality(), 90); // 其他格式默认质量
    }
    
    #[test]
    fn test_get_effective_frame_rate() {
        let mut config = CaptureConfig::default();
        
        // 测试指定帧率
        config.frame_rate = Some(60);
        assert_eq!(config.get_effective_frame_rate(), 60);
        
        // 测试超出范围的帧率
        config.frame_rate = Some(150);
        assert_eq!(config.get_effective_frame_rate(), 120); // 应该被限制在120
        
        config.frame_rate = Some(0);
        assert_eq!(config.get_effective_frame_rate(), 1); // 应该被限制在1
        
        // 测试默认帧率
        config.frame_rate = None;
        assert_eq!(config.get_effective_frame_rate(), 30); // 默认30fps
    }
}
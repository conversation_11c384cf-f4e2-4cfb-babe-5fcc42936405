//! 主要的屏幕捕获API

use crate::config::CaptureConfig;
use crate::error::{CaptureError, CaptureResult, Result};
use crate::controller::CaptureController;
use crate::logging::{LogConfig, init_logging};

/// 屏幕捕获主要接口
#[derive(Debug)]
pub struct ScreenCapture {
    config: CaptureConfig,
    controller: CaptureController,
}

impl ScreenCapture {
    /// 创建新的屏幕捕获实例
    pub async fn new(mut config: CaptureConfig) -> Result<Self> {
        // 初始化日志系统
        Self::init_logging(&config)?;

        // 验证配置
        config.validate()?;

        // 应用默认值
        config.apply_defaults();

        // 创建操作控制器
        let controller = CaptureController::new().await?;
        
        Ok(Self {
            config,
            controller,
        })
    }
    
    /// 执行捕获操作
    pub async fn execute(&mut self) -> Result<CaptureResult> {
        crate::log_operation_start!("屏幕捕获");

        let start_time = std::time::Instant::now();

        let result = match self.config.operation_type {
            crate::config::OperationType::Screenshot => {
                self.controller.screenshot(self.config.clone()).await
            },
            crate::config::OperationType::Recording => {
                self.controller.record(self.config.clone()).await
            },
            crate::config::OperationType::IntervalScreenshot => {
                self.controller.interval_screenshot(self.config.clone()).await
            },
        };

        match result {
            Ok(capture_result) => {
                let duration = start_time.elapsed();
                crate::log_operation_complete!("屏幕捕获", duration);
                Ok(capture_result)
            },
            Err(e) => {
                let duration = start_time.elapsed();
                crate::log_operation_error!("屏幕捕获", e);
                Err(e)
            }
        }
    }
    
    /// 停止当前操作
    pub async fn stop(&mut self) -> Result<()> {
        self.controller.stop().await
    }
    
    /// 获取当前配置
    pub fn config(&self) -> &CaptureConfig {
        &self.config
    }
    
    /// 更新配置
    pub fn update_config(&mut self, mut config: CaptureConfig) -> Result<()> {
        config.validate()?;
        config.apply_defaults();
        
        self.config = config;
        
        Ok(())
    }
    
    /// 检查是否正在执行操作
    pub fn is_active(&self) -> bool {
        self.controller.is_busy()
    }

    /// 初始化日志系统
    fn init_logging(config: &CaptureConfig) -> Result<()> {
        // 避免重复初始化
        static INIT_ONCE: std::sync::Once = std::sync::Once::new();
        static mut INIT_RESULT: Option<Result<()>> = None;

        unsafe {
            INIT_ONCE.call_once(|| {
                let log_config = LogConfig::new()
                    .with_verbose(config.verbose_logging)
                    .with_timestamp(true)
                    .with_target(config.verbose_logging);

                let log_config = if let Some(ref log_path) = config.log_file_path {
                    log_config.with_log_file(log_path.clone())
                } else {
                    log_config
                };

                INIT_RESULT = Some(
                    init_logging(log_config)
                        .map_err(|e| CaptureError::System(format!("日志初始化失败: {}", e)))
                );
            });

            INIT_RESULT.as_ref().unwrap().clone()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{OperationType, OutputFormat};
    use std::path::PathBuf;

    #[test]
    fn test_screen_capture_creation() {
        let config = CaptureConfig {
            operation_type: OperationType::Screenshot,
            output_directory: std::env::temp_dir(),
            output_format: OutputFormat::PNG,
            ..Default::default()
        };
        
        // 注意：这个测试可能会失败，因为我们还没有实现OperationController
        // 这是正常的，我们会在后续任务中实现它
        let result = ScreenCapture::new(config);
        // assert!(result.is_ok());
    }
}
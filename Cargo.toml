[package]
name = "wayland-screen-capture"
version = "0.1.0"
edition = "2021"
authors = ["l<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "A Rust library for screen capture and recording on Linux Wayland environments"
license = "MIT OR Apache-2.0"
repository = "https://github.com/liuwenwu/wayland-screen-capture"
keywords = ["wayland", "screen-capture", "recording", "linux", "gnome"]
categories = ["multimedia", "os::unix-apis"]

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# D-Bus通信
zbus = "3.0"

# PipeWire集成
pipewire = { version = "0.8", optional = true }

# 视频编码
gstreamer = "0.20"
gstreamer-app = "0.20"

# 图像处理
image = "0.24"

# 错误处理
thiserror = "1.0"

# 日志记录
tracing = "0.1"
log = "0.4"

# 序列化
serde = { version = "1.0", features = ["derive"] }

# UUID生成
uuid = { version = "1.0", features = ["v4"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 异步trait支持
async-trait = "0.1"

# X11支持
x11rb = { version = "0.12", features = ["all-extensions", "allow-unsafe-code"] }

# 系统调用
nix = { version = "0.27", features = ["fs"] }

# 异步工具
futures-util = "0.3"

[features]
default = ["x11"]
wayland = ["pipewire"]
x11 = []

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
tempfile = "3.0"
criterion = "0.5"
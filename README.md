# Wayland Screen Capture

一个用于在Linux Wayland环境下进行屏幕截图和录制的Rust库。

## 特性

- 🖼️ 屏幕截图支持（PNG、JPEG、BMP、WebP）
- 🎥 屏幕录制支持（MP4、WebM、AVI、MKV）
- ⏱️ 定时录制功能
- 📸 间隔截图功能
- 🔒 通过xdg-desktop-portal处理权限申请
- ⚡ 异步API设计
- 🛡️ 遵循Wayland安全模型

## 系统要求

- Linux内核 5.10+
- Wayland合成器支持
- xdg-desktop-portal安装
- PipeWire 0.3+

## 运行时依赖

- libpipewire-0.3
- libgstreamer-1.0
- xdg-desktop-portal-gtk 或 xdg-desktop-portal-kde

## 基本使用

```rust
use wayland_screen_capture::{ScreenCapture, CaptureConfig, OperationType, OutputFormat};
use std::path::PathBuf;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 配置截图
    let config = CaptureConfig {
        operation_type: OperationType::Screenshot,
        output_directory: PathBuf::from("/tmp"),
        output_format: OutputFormat::PNG,
        ..Default::default()
    };
    
    // 创建捕获实例
    let mut capture = ScreenCapture::new(config)?;
    
    // 执行截图
    let result = capture.execute().await?;
    
    println!("截图保存到: {:?}", result.output_files);
    Ok(())
}
```

## 项目结构

```
src/
├── lib.rs              # 库入口和公共API
├── config.rs           # 配置管理
├── error.rs            # 错误类型定义
├── capture.rs          # 主要捕获API
├── controller.rs       # 操作控制器
├── permission.rs       # 权限管理
├── backend.rs          # 捕获后端
├── file_manager.rs     # 文件管理
└── utils.rs            # 工具函数
```

## 核心组件

### 配置管理 (config.rs)
- `CaptureConfig`: 捕获配置结构
- `OperationType`: 操作类型枚举
- `OutputFormat`: 输出格式枚举

### 错误处理 (error.rs)
- `CaptureError`: 主要错误类型
- `CaptureResult`: 操作结果结构
- 详细的错误分类和处理

### 权限管理 (permission.rs)
- `PermissionManager`: 权限管理trait
- `XdgPortalPermissionManager`: xdg-desktop-portal实现
- `PermissionToken`: 权限令牌

### 捕获后端 (backend.rs)
- `CaptureBackend`: 捕获后端trait
- `PipeWireCaptureBackend`: PipeWire实现
- `MockCaptureBackend`: 测试用模拟后端

### 文件管理 (file_manager.rs)
- 文件保存和管理
- 文件名生成
- 磁盘空间检查

### 操作控制 (controller.rs)
- `OperationController`: 操作协调器
- 状态管理
- 操作流程控制

## 开发状态

这是一个正在开发中的项目。当前已完成：

- ✅ 项目结构和核心接口定义
- ⏳ 配置管理和验证（进行中）
- ⏳ 权限管理模块（待实现）
- ⏳ 文件管理模块（待实现）
- ⏳ 屏幕捕获后端（待实现）
- ⏳ 视频编码模块（待实现）
- ⏳ 操作控制器（待实现）

## 许可证

MIT
//! 基本使用示例

use wayland_screen_capture::{ScreenCapture, CaptureConfig, OperationType, OutputFormat};
use std::path::PathBuf;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::init();
    
    println!("Wayland Screen Capture 示例");
    
    // 示例1: 基本截图
    println!("\n=== 示例1: 基本截图 ===");
    let config = CaptureConfig {
        operation_type: OperationType::Screenshot,
        output_directory: std::env::temp_dir(),
        output_format: OutputFormat::PNG,
        ..Default::default()
    };
    
    match ScreenCapture::new(config).await {
        Ok(mut capture) => {
            match capture.execute().await {
                Ok(result) => {
                    println!("截图成功!");
                    println!("输出文件: {:?}", result.output_files);
                    println!("耗时: {:?}", result.duration);
                },
                Err(e) => println!("截图失败: {}", e),
            }
        },
        Err(e) => println!("创建捕获实例失败: {}", e),
    }
    
    // 示例2: 定时录制
    println!("\n=== 示例2: 定时录制 (5秒) ===");
    let config = CaptureConfig {
        operation_type: OperationType::Recording,
        output_directory: std::env::temp_dir(),
        output_format: OutputFormat::MP4,
        duration: Some(Duration::from_secs(5)),
        ..Default::default()
    };
    
    match ScreenCapture::new(config).await {
        Ok(mut capture) => {
            match capture.execute().await {
                Ok(result) => {
                    println!("录制成功!");
                    println!("输出文件: {:?}", result.output_files);
                    println!("耗时: {:?}", result.duration);
                },
                Err(e) => println!("录制失败: {}", e),
            }
        },
        Err(e) => println!("创建捕获实例失败: {}", e),
    }
    
    // 示例3: 间隔截图
    println!("\n=== 示例3: 间隔截图 (每2秒截图一次，持续10秒) ===");
    let config = CaptureConfig {
        operation_type: OperationType::IntervalScreenshot,
        output_directory: std::env::temp_dir(),
        output_format: OutputFormat::PNG,
        interval: Some(Duration::from_secs(2)),
        duration: Some(Duration::from_secs(10)),
        ..Default::default()
    };
    
    match ScreenCapture::new(config).await {
        Ok(mut capture) => {
            match capture.execute().await {
                Ok(result) => {
                    println!("间隔截图成功!");
                    println!("输出文件: {:?}", result.output_files);
                    println!("总帧数: {}", result.total_frames.unwrap_or(0));
                    println!("耗时: {:?}", result.duration);
                },
                Err(e) => println!("间隔截图失败: {}", e),
            }
        },
        Err(e) => println!("创建捕获实例失败: {}", e),
    }
    
    Ok(())
}

//! 功能测试示例
//! 
//! 测试截图和录屏功能的完整性

use wayland_screen_capture::{
    ScreenCapture, CaptureConfig, OperationType, OutputFormat,
    init_verbose_logging
};
use std::path::PathBuf;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化详细日志
    if let Err(e) = init_verbose_logging() {
        println!("警告: 日志初始化失败: {}", e);
    }
    
    println!("=== Wayland Screen Capture 功能测试 ===\n");
    
    // 创建测试输出目录
    let test_dir = std::env::temp_dir().join("wl-capture-test");
    std::fs::create_dir_all(&test_dir)?;
    println!("测试输出目录: {:?}\n", test_dir);
    
    // 测试1: 基本截图功能
    println!("🔍 测试1: 基本截图功能");
    test_screenshot(&test_dir).await?;
    
    // 测试2: 不同格式截图
    println!("\n🖼️  测试2: 不同格式截图");
    test_different_formats(&test_dir).await?;
    
    // 测试3: 短时间录制
    println!("\n🎥 测试3: 短时间录制 (3秒)");
    test_short_recording(&test_dir).await?;
    
    // 测试4: 间隔截图
    println!("\n📸 测试4: 间隔截图 (每2秒截图，持续6秒)");
    test_interval_screenshot(&test_dir).await?;
    
    // 测试5: 配置验证
    println!("\n⚙️  测试5: 配置验证");
    test_config_validation().await?;
    
    println!("\n✅ 所有测试完成！");
    println!("测试文件保存在: {:?}", test_dir);
    
    Ok(())
}

async fn test_screenshot(output_dir: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
    let config = CaptureConfig {
        operation_type: OperationType::Screenshot,
        output_directory: output_dir.clone(),
        output_format: OutputFormat::PNG,
        filename_prefix: Some("test_screenshot".to_string()),
        verbose_logging: true,
        ..Default::default()
    };
    
    match ScreenCapture::new(config).await {
        Ok(mut capture) => {
            match capture.execute().await {
                Ok(result) => {
                    println!("  ✅ 截图成功!");
                    println!("     输出文件: {:?}", result.output_files);
                    println!("     耗时: {:?}", result.duration);
                    
                    // 验证文件是否存在
                    if let Some(file_path) = result.output_files.first() {
                        if file_path.exists() {
                            let metadata = std::fs::metadata(file_path)?;
                            println!("     文件大小: {} bytes", metadata.len());
                        } else {
                            println!("  ⚠️  警告: 输出文件不存在");
                        }
                    }
                },
                Err(e) => {
                    println!("  ❌ 截图失败: {}", e);
                    return Err(e.into());
                }
            }
        },
        Err(e) => {
            println!("  ❌ 创建截图实例失败: {}", e);
            return Err(e.into());
        }
    }
    
    Ok(())
}

async fn test_different_formats(output_dir: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
    let formats = vec![
        (OutputFormat::PNG, "png"),
        (OutputFormat::JPEG, "jpeg"),
        (OutputFormat::BMP, "bmp"),
    ];
    
    for (format, name) in formats {
        println!("  测试 {} 格式...", name.to_uppercase());
        
        let config = CaptureConfig {
            operation_type: OperationType::Screenshot,
            output_directory: output_dir.clone(),
            output_format: format,
            filename_prefix: Some(format!("test_{}", name)),
            quality: Some(90),
            verbose_logging: false,
            ..Default::default()
        };
        
        match ScreenCapture::new(config).await {
            Ok(mut capture) => {
                match capture.execute().await {
                    Ok(result) => {
                        println!("    ✅ {} 格式截图成功", name.to_uppercase());
                        if let Some(file_path) = result.output_files.first() {
                            if file_path.exists() {
                                let metadata = std::fs::metadata(file_path)?;
                                println!("       文件大小: {} bytes", metadata.len());
                            }
                        }
                    },
                    Err(e) => {
                        println!("    ❌ {} 格式截图失败: {}", name.to_uppercase(), e);
                    }
                }
            },
            Err(e) => {
                println!("    ❌ 创建 {} 格式截图实例失败: {}", name.to_uppercase(), e);
            }
        }
    }
    
    Ok(())
}

async fn test_short_recording(output_dir: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
    let config = CaptureConfig {
        operation_type: OperationType::Recording,
        output_directory: output_dir.clone(),
        output_format: OutputFormat::MP4,
        duration: Some(Duration::from_secs(3)),
        filename_prefix: Some("test_recording".to_string()),
        frame_rate: Some(30),
        verbose_logging: true,
        ..Default::default()
    };
    
    println!("  开始录制 (3秒)...");
    
    match ScreenCapture::new(config).await {
        Ok(mut capture) => {
            match capture.execute().await {
                Ok(result) => {
                    println!("  ✅ 录制成功!");
                    println!("     输出文件: {:?}", result.output_files);
                    println!("     耗时: {:?}", result.duration);
                    println!("     总帧数: {}", result.total_frames.unwrap_or(0));
                    
                    // 验证文件是否存在
                    if let Some(file_path) = result.output_files.first() {
                        if file_path.exists() {
                            let metadata = std::fs::metadata(file_path)?;
                            println!("     文件大小: {} bytes", metadata.len());
                        } else {
                            println!("  ⚠️  警告: 输出文件不存在");
                        }
                    }
                },
                Err(e) => {
                    println!("  ❌ 录制失败: {}", e);
                    return Err(e.into());
                }
            }
        },
        Err(e) => {
            println!("  ❌ 创建录制实例失败: {}", e);
            return Err(e.into());
        }
    }
    
    Ok(())
}

async fn test_interval_screenshot(output_dir: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
    let config = CaptureConfig {
        operation_type: OperationType::IntervalScreenshot,
        output_directory: output_dir.clone(),
        output_format: OutputFormat::PNG,
        interval: Some(Duration::from_secs(2)),
        duration: Some(Duration::from_secs(6)),
        filename_prefix: Some("test_interval".to_string()),
        verbose_logging: true,
        ..Default::default()
    };
    
    println!("  开始间隔截图 (每2秒截图，持续6秒)...");
    
    match ScreenCapture::new(config).await {
        Ok(mut capture) => {
            match capture.execute().await {
                Ok(result) => {
                    println!("  ✅ 间隔截图成功!");
                    println!("     输出文件数量: {}", result.output_files.len());
                    println!("     耗时: {:?}", result.duration);
                    println!("     总帧数: {}", result.total_frames.unwrap_or(0));
                    
                    // 验证文件
                    for (i, file_path) in result.output_files.iter().enumerate() {
                        if file_path.exists() {
                            let metadata = std::fs::metadata(file_path)?;
                            println!("     文件 {}: {} bytes", i + 1, metadata.len());
                        } else {
                            println!("  ⚠️  警告: 文件 {} 不存在", i + 1);
                        }
                    }
                },
                Err(e) => {
                    println!("  ❌ 间隔截图失败: {}", e);
                    return Err(e.into());
                }
            }
        },
        Err(e) => {
            println!("  ❌ 创建间隔截图实例失败: {}", e);
            return Err(e.into());
        }
    }
    
    Ok(())
}

async fn test_config_validation() -> Result<(), Box<dyn std::error::Error>> {
    println!("  测试无效配置...");
    
    // 测试无效的输出目录
    let invalid_config = CaptureConfig {
        operation_type: OperationType::Screenshot,
        output_directory: PathBuf::from("/invalid/path/that/does/not/exist"),
        output_format: OutputFormat::PNG,
        ..Default::default()
    };
    
    match ScreenCapture::new(invalid_config).await {
        Ok(_) => {
            println!("  ⚠️  警告: 无效配置应该被拒绝");
        },
        Err(e) => {
            println!("  ✅ 正确拒绝了无效配置: {}", e);
        }
    }
    
    println!("  测试有效配置...");
    
    // 测试有效配置
    let valid_config = CaptureConfig {
        operation_type: OperationType::Screenshot,
        output_directory: std::env::temp_dir(),
        output_format: OutputFormat::PNG,
        verbose_logging: false,
        ..Default::default()
    };
    
    match ScreenCapture::new(valid_config).await {
        Ok(_) => {
            println!("  ✅ 有效配置被正确接受");
        },
        Err(e) => {
            println!("  ❌ 有效配置被错误拒绝: {}", e);
            return Err(e.into());
        }
    }
    
    Ok(())
}
